# jshERP故障排除指南

## 概述

本文档提供jshERP系统常见问题的诊断和解决方案，适用于宝塔面板部署的生产环境。

## 快速诊断

### 系统状态检查

```bash
# 检查服务状态
systemctl status nginx
systemctl status mysql
systemctl status redis

# 检查端口监听
netstat -tlnp | grep -E ":3000|:9999|:3306|:6379"

# 检查进程
ps aux | grep -E "java|nginx|mysql|redis"

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 日志文件位置

```bash
# jshERP应用日志
tail -f /opt/jshERP/logs/jshERP.log

# Nginx访问日志
tail -f /opt/jshERP/logs/nginx_access.log

# Nginx错误日志
tail -f /opt/jshERP/logs/nginx_error.log

# MySQL错误日志
tail -f /var/log/mysql/error.log

# 系统日志
tail -f /var/log/messages
```

## 常见问题及解决方案

### 1. 前端访问问题

#### 问题：无法访问前端页面（404错误）

**可能原因：**
- Nginx配置错误
- 前端文件未正确部署
- 端口被占用

**解决步骤：**

1. 检查Nginx配置
```bash
nginx -t
systemctl reload nginx
```

2. 检查前端文件
```bash
ls -la /home/<USER>/jshERP-web/
# 应该包含index.html和static目录
```

3. 检查端口占用
```bash
netstat -tlnp | grep :3000
```

4. 重启Nginx
```bash
systemctl restart nginx
```

#### 问题：前端页面空白或加载失败

**可能原因：**
- 静态资源路径错误
- 缓存问题
- 构建文件损坏

**解决步骤：**

1. 清除浏览器缓存
2. 检查浏览器控制台错误
3. 验证静态资源路径
```bash
curl -I http://localhost:3000/static/js/app.js
```

4. 重新部署前端
```bash
cd /home/<USER>/jshERP-web
rm -rf *
unzip -o /path/to/dist.zip
```

### 2. 后端服务问题

#### 问题：后端服务无法启动

**可能原因：**
- Java环境问题
- 配置文件错误
- 端口被占用
- 数据库连接失败

**解决步骤：**

1. 检查Java版本
```bash
java -version
# 需要JDK 1.8+
```

2. 检查配置文件
```bash
cd /home/<USER>/jshERP-boot
cat config/application-production.properties
```

3. 检查端口占用
```bash
netstat -tlnp | grep :9999
```

4. 手动启动并查看错误
```bash
cd /home/<USER>/jshERP-boot
java -jar lib/jshERP.jar --spring.profiles.active=production
```

5. 检查启动脚本权限
```bash
chmod +x start.sh restart.sh stop.sh
```

#### 问题：后端服务频繁重启

**可能原因：**
- 内存不足
- 数据库连接超时
- 配置错误

**解决步骤：**

1. 检查内存使用
```bash
free -h
top -p $(pgrep -f jshERP.jar)
```

2. 调整JVM参数
```bash
# 编辑start.sh，添加JVM参数
-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m
```

3. 检查数据库连接
```bash
mysql -h127.0.0.1 -ujsh_user -p123456 jsh_erp -e "SELECT 1"
```

### 3. 数据库问题

#### 问题：数据库连接失败

**可能原因：**
- MySQL服务未启动
- 用户权限问题
- 网络连接问题
- 配置错误

**解决步骤：**

1. 检查MySQL服务
```bash
systemctl status mysql
systemctl start mysql
```

2. 检查用户权限
```bash
mysql -u root -p
SHOW GRANTS FOR 'jsh_user'@'%';
GRANT ALL PRIVILEGES ON jsh_erp.* TO 'jsh_user'@'%';
FLUSH PRIVILEGES;
```

3. 检查防火墙
```bash
iptables -L | grep 3306
```

4. 测试连接
```bash
telnet 127.0.0.1 3306
```

#### 问题：数据库性能慢

**可能原因：**
- 缺少索引
- 表数据量大
- 配置不当

**解决步骤：**

1. 运行维护脚本
```bash
cd /path/to/database
./maintenance.sh
```

2. 检查慢查询日志
```bash
mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log%'"
```

3. 优化MySQL配置
```bash
# 编辑/etc/mysql/mysql.conf.d/mysqld.cnf
innodb_buffer_pool_size = 1G
query_cache_size = 256M
```

### 4. Redis问题

#### 问题：Redis连接失败

**可能原因：**
- Redis服务未启动
- 密码错误
- 配置问题

**解决步骤：**

1. 检查Redis服务
```bash
systemctl status redis
systemctl start redis
```

2. 测试连接
```bash
redis-cli -h 127.0.0.1 -p 6379 -a 1234abcd ping
```

3. 检查配置文件
```bash
grep -E "bind|port|requirepass" /etc/redis/redis.conf
```

### 5. 权限问题

#### 问题：文件上传失败

**可能原因：**
- 目录权限不足
- 磁盘空间不足
- 文件大小限制

**解决步骤：**

1. 检查目录权限
```bash
ls -la /opt/jshERP/upload
chown -R www:www /opt/jshERP/upload
chmod -R 755 /opt/jshERP/upload
```

2. 检查磁盘空间
```bash
df -h /opt/jshERP/upload
```

3. 检查文件大小限制
```bash
# Nginx配置
client_max_body_size 10m;

# 应用配置
spring.servlet.multipart.max-file-size=10485760
```

#### 问题：功能按钮不显示

**可能原因：**
- 权限配置错误
- 角色分配问题
- 缓存问题

**解决步骤：**

1. 运行权限修复脚本
```bash
cd /path/to/scripts
./fix-button-permissions.sh
```

2. 清除Redis缓存
```bash
redis-cli -h 127.0.0.1 -p 6379 -a 1234abcd FLUSHDB
```

3. 重启应用服务
```bash
cd /home/<USER>/jshERP-boot
./restart.sh
```

## 性能优化

### 1. 数据库优化

```sql
-- 定期优化表
OPTIMIZE TABLE jsh_depot_item;
OPTIMIZE TABLE jsh_depot_head;
OPTIMIZE TABLE jsh_material;

-- 分析表统计信息
ANALYZE TABLE jsh_depot_item;
ANALYZE TABLE jsh_depot_head;

-- 清理过期日志
DELETE FROM jsh_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 2. 应用优化

```bash
# JVM参数优化
-Xms1024m -Xmx2048m
-XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m
-XX:+UseG1GC -XX:MaxGCPauseMillis=200

# 连接池优化
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
```

### 3. Nginx优化

```nginx
# 启用gzip压缩
gzip on;
gzip_comp_level 6;
gzip_types text/plain text/css application/json application/javascript;

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 监控和维护

### 1. 定期检查

```bash
# 每日检查脚本
#!/bin/bash
echo "=== $(date) ==="
echo "磁盘使用率:"
df -h | grep -E "/$|/opt"
echo "内存使用率:"
free -h
echo "服务状态:"
systemctl is-active nginx mysql redis
echo "应用进程:"
pgrep -f jshERP.jar || echo "应用未运行"
```

### 2. 自动备份

```bash
# 添加到crontab
0 2 * * * /path/to/database/backup-database.sh
0 3 * * 0 /path/to/database/maintenance.sh
```

### 3. 日志轮转

```bash
# 配置logrotate
/opt/jshERP/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www www
}
```

## 紧急恢复

### 1. 服务恢复

```bash
# 快速重启所有服务
systemctl restart nginx
systemctl restart mysql
systemctl restart redis
cd /home/<USER>/jshERP-boot && ./restart.sh
```

### 2. 数据恢复

```bash
# 从备份恢复数据库
cd /path/to/database
./restore-database.sh /opt/jshERP/backup/latest_backup.sql.gz
```

### 3. 配置恢复

```bash
# 恢复默认配置
cp /path/to/config/application-production.properties /home/<USER>/jshERP-boot/config/
cp /path/to/config/nginx-production.conf /etc/nginx/sites-available/jshERP
```

## 联系支持

如果以上方案无法解决问题，请收集以下信息：

1. 错误日志（最近100行）
2. 系统配置信息
3. 问题复现步骤
4. 环境信息（操作系统、软件版本等）

```bash
# 收集系统信息脚本
echo "=== 系统信息 ==="
uname -a
cat /etc/os-release
echo "=== 软件版本 ==="
java -version
nginx -v
mysql --version
redis-server --version
echo "=== 服务状态 ==="
systemctl status nginx mysql redis
echo "=== 最近错误日志 ==="
tail -50 /opt/jshERP/logs/jshERP.log
```
