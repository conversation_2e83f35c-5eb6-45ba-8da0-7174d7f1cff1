#!/bin/bash

# jshERP自动化部署脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 一键部署jshERP系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
DEPLOY_DIR="/home/<USER>"
PACKAGE_NAME="jshERP3.5-最新包"
MYSQL_ROOT_PASSWORD=""
MYSQL_USER="jsh_user"
MYSQL_PASSWORD="123456"
MYSQL_DATABASE="jsh_erp"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示部署信息
show_deploy_info() {
    echo "=================================================="
    echo "    jshERP自动化部署脚本"
    echo "=================================================="
    echo "部署目录: $DEPLOY_DIR"
    echo "数据库: $MYSQL_DATABASE"
    echo "数据库用户: $MYSQL_USER"
    echo "=================================================="
    echo ""
}

# 检查运行权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统环境
check_environment() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/redhat-release ]]; then
        log_warning "建议在CentOS 7.5环境下运行"
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装JDK 1.8"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    log_info "Java版本: $java_version"
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL未安装，请先安装MySQL 5.7"
        exit 1
    fi
    
    # 检查MySQL服务
    if ! pgrep -f mysqld > /dev/null; then
        log_error "MySQL服务未运行，请启动MySQL服务"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检测宝塔面板
detect_bt_panel() {
    if [[ -d "/www/server" ]]; then
        log_info "检测到宝塔面板环境"
        export BT_PANEL=true
        export MYSQL_CMD="/www/server/mysql/bin/mysql"
    else
        log_info "标准Linux环境"
        export BT_PANEL=false
        export MYSQL_CMD="mysql"
    fi
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    # 创建主目录
    mkdir -p "$DEPLOY_DIR"/{jshERP-boot,jshERP-web}
    
    # 创建日志和上传目录
    mkdir -p /opt/jshERP/{logs,upload}
    mkdir -p /opt/tmp/tomcat
    
    # 设置权限
    chown -R root:root "$DEPLOY_DIR"
    chown -R root:root /opt/jshERP
    chmod -R 755 "$DEPLOY_DIR"
    chmod -R 755 /opt/jshERP
    
    log_success "目录结构创建完成"
}

# 解压部署包
extract_packages() {
    log_step "解压部署包..."
    
    if [[ ! -d "$PACKAGE_NAME" ]]; then
        log_error "部署包目录不存在: $PACKAGE_NAME"
        exit 1
    fi
    
    # 解压前端包
    if [[ -f "$PACKAGE_NAME/前端包/dist.zip" ]]; then
        cd "$DEPLOY_DIR/jshERP-web"
        unzip -q "$OLDPWD/$PACKAGE_NAME/前端包/dist.zip"
        log_success "前端包解压完成"
        cd "$OLDPWD"
    else
        log_error "前端包不存在"
        exit 1
    fi
    
    # 解压后端包
    if [[ -f "$PACKAGE_NAME/后端包/jshERP-bin.zip" ]]; then
        cd "$DEPLOY_DIR/jshERP-boot"
        unzip -q "$OLDPWD/$PACKAGE_NAME/后端包/jshERP-bin.zip"
        log_success "后端包解压完成"
        cd "$OLDPWD"
    else
        log_error "后端包不存在"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    
    # 获取MySQL root密码
    if [[ -z "$MYSQL_ROOT_PASSWORD" ]]; then
        read -s -p "请输入MySQL root密码: " MYSQL_ROOT_PASSWORD
        echo ""
    fi
    
    # 执行数据库初始化脚本
    if [[ -f "$PACKAGE_NAME/数据库/init-database.sql" ]]; then
        $MYSQL_CMD -u root -p"$MYSQL_ROOT_PASSWORD" < "$PACKAGE_NAME/数据库/init-database.sql"
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在，手动创建数据库和用户"
        
        # 手动创建数据库和用户
        $MYSQL_CMD -u root -p"$MYSQL_ROOT_PASSWORD" -e "
        CREATE DATABASE IF NOT EXISTS $MYSQL_DATABASE CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE USER IF NOT EXISTS '$MYSQL_USER'@'localhost' IDENTIFIED BY '$MYSQL_PASSWORD';
        GRANT ALL PRIVILEGES ON $MYSQL_DATABASE.* TO '$MYSQL_USER'@'localhost';
        FLUSH PRIVILEGES;
        "
        log_success "数据库和用户创建完成"
    fi
    
    # 导入数据
    if [[ -f "$PACKAGE_NAME/jsh_erp.sql" ]]; then
        $MYSQL_CMD -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < "$PACKAGE_NAME/jsh_erp.sql"
        log_success "数据导入完成"
    else
        log_error "数据库文件不存在: jsh_erp.sql"
        exit 1
    fi
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."
    
    if [[ -f "$PACKAGE_NAME/nginx配置文件-参考/nginx.conf" ]]; then
        if [[ "$BT_PANEL" == "true" ]]; then
            log_info "宝塔面板环境，请手动在面板中配置Nginx"
            log_info "参考配置文件: $PACKAGE_NAME/nginx配置文件-参考/nginx.conf"
        else
            # 标准环境配置Nginx
            if command -v nginx &> /dev/null; then
                cp "$PACKAGE_NAME/nginx配置文件-参考/nginx.conf" /etc/nginx/nginx.conf.jsh_backup
                log_info "Nginx配置文件已备份到: /etc/nginx/nginx.conf.jsh_backup"
                log_warning "请手动配置Nginx或使用宝塔面板"
            else
                log_warning "Nginx未安装，请先安装Nginx"
            fi
        fi
    fi
}

# 启动服务
start_services() {
    log_step "启动jshERP服务..."
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/jshERP-boot/start.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
nohup java -jar lib/jshERP.jar --spring.profiles.active=production > logs/jshERP.log 2>&1 &
echo $! > jshERP.pid
echo "jshERP服务已启动，PID: $(cat jshERP.pid)"
EOF
    
    # 创建停止脚本
    cat > "$DEPLOY_DIR/jshERP-boot/stop.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
if [[ -f jshERP.pid ]]; then
    PID=$(cat jshERP.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "jshERP服务已停止"
    else
        echo "jshERP服务未运行"
    fi
    rm -f jshERP.pid
else
    echo "PID文件不存在"
fi
EOF
    
    chmod +x "$DEPLOY_DIR/jshERP-boot"/{start.sh,stop.sh}
    
    # 启动服务
    cd "$DEPLOY_DIR/jshERP-boot"
    ./start.sh
    
    log_success "jshERP服务启动完成"
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 等待服务启动
    sleep 10
    
    # 检查Java进程
    if pgrep -f "jshERP.jar" > /dev/null; then
        log_success "✓ jshERP进程运行正常"
    else
        log_error "✗ jshERP进程未运行"
        return 1
    fi
    
    # 检查端口
    if netstat -tlnp | grep :9999 > /dev/null; then
        log_success "✓ 端口9999监听正常"
    else
        log_error "✗ 端口9999未监听"
        return 1
    fi
    
    # 检查数据库连接
    if $MYSQL_CMD -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE; SELECT COUNT(*) FROM jsh_user;" > /dev/null 2>&1; then
        log_success "✓ 数据库连接正常"
    else
        log_error "✗ 数据库连接失败"
        return 1
    fi
    
    log_success "部署验证通过"
}

# 显示部署结果
show_result() {
    echo ""
    echo "=================================================="
    log_success "jshERP部署完成！"
    echo "=================================================="
    echo "访问地址:"
    echo "- 前端: http://$(hostname -I | awk '{print $1}'):3000"
    echo "- 后端: http://$(hostname -I | awk '{print $1}'):9999/jshERP-boot"
    echo ""
    echo "默认账户:"
    echo "- 管理员: admin / 123456"
    echo "- 主账户: waterxi / 123456"
    echo ""
    echo "服务管理:"
    echo "- 启动: $DEPLOY_DIR/jshERP-boot/start.sh"
    echo "- 停止: $DEPLOY_DIR/jshERP-boot/stop.sh"
    echo "- 日志: $DEPLOY_DIR/jshERP-boot/logs/jshERP.log"
    echo ""
    echo "注意事项:"
    echo "1. 请配置Nginx代理前端访问"
    echo "2. 生产环境请修改默认密码"
    echo "3. 定期备份数据库和上传文件"
    echo "=================================================="
}

# 主函数
main() {
    show_deploy_info
    check_permissions
    check_environment
    detect_bt_panel
    create_directories
    extract_packages
    init_database
    configure_nginx
    start_services
    
    if verify_deployment; then
        show_result
    else
        log_error "部署验证失败，请检查日志"
        exit 1
    fi
}

# 执行主函数
main "$@"
