#!/bin/bash

# jshERP服务启动脚本
# 作者: Augment Code
# 版本: 1.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
APP_NAME="jshERP"
APP_JAR="lib/jshERP.jar"
APP_PROFILE="production"
PID_FILE="jshERP.pid"
LOG_FILE="logs/jshERP.log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装JDK 1.8"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    log_info "Java版本: $java_version"
}

# 检查应用文件
check_app_files() {
    if [[ ! -f "$APP_JAR" ]]; then
        log_error "应用文件不存在: $APP_JAR"
        exit 1
    fi
    
    if [[ ! -d "config" ]]; then
        log_error "配置目录不存在: config"
        exit 1
    fi
    
    if [[ ! -f "config/application-$APP_PROFILE.properties" ]]; then
        log_error "配置文件不存在: config/application-$APP_PROFILE.properties"
        exit 1
    fi
}

# 检查服务状态
check_status() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 服务正在运行
        else
            rm -f "$PID_FILE"
            return 1  # PID文件存在但进程不存在
        fi
    else
        return 1  # PID文件不存在
    fi
}

# 启动服务
start_service() {
    log_info "启动$APP_NAME服务..."
    
    # 检查服务是否已运行
    if check_status; then
        local pid=$(cat "$PID_FILE")
        log_warning "$APP_NAME服务已在运行 (PID: $pid)"
        return 0
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动应用
    nohup java -jar "$APP_JAR" --spring.profiles.active="$APP_PROFILE" > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待启动
    log_info "等待服务启动..."
    sleep 5
    
    # 检查启动状态
    if kill -0 "$pid" 2>/dev/null; then
        log_success "$APP_NAME服务启动成功 (PID: $pid)"
        log_info "日志文件: $LOG_FILE"
        
        # 等待端口监听
        local count=0
        while [[ $count -lt 30 ]]; do
            if netstat -tlnp 2>/dev/null | grep :9999 > /dev/null; then
                log_success "端口9999监听成功"
                break
            fi
            sleep 2
            ((count++))
        done
        
        if [[ $count -eq 30 ]]; then
            log_warning "端口9999未在预期时间内启动，请检查日志"
        fi
        
    else
        log_error "$APP_NAME服务启动失败"
        rm -f "$PID_FILE"
        log_info "请检查日志文件: $LOG_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止$APP_NAME服务..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            
            # 等待进程结束
            local count=0
            while [[ $count -lt 10 ]]; do
                if ! kill -0 "$pid" 2>/dev/null; then
                    break
                fi
                sleep 1
                ((count++))
            done
            
            # 强制杀死进程
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "强制停止进程"
                kill -9 "$pid"
            fi
            
            log_success "$APP_NAME服务已停止"
        else
            log_warning "$APP_NAME服务未运行"
        fi
        rm -f "$PID_FILE"
    else
        log_warning "PID文件不存在，$APP_NAME服务可能未运行"
    fi
}

# 重启服务
restart_service() {
    log_info "重启$APP_NAME服务..."
    stop_service
    sleep 2
    start_service
}

# 查看状态
show_status() {
    if check_status; then
        local pid=$(cat "$PID_FILE")
        log_success "$APP_NAME服务正在运行 (PID: $pid)"
        
        # 显示端口信息
        if netstat -tlnp 2>/dev/null | grep :9999 > /dev/null; then
            log_info "端口9999正在监听"
        else
            log_warning "端口9999未监听"
        fi
        
        # 显示内存使用
        local memory=$(ps -p "$pid" -o rss= 2>/dev/null | awk '{print int($1/1024)"MB"}')
        if [[ -n "$memory" ]]; then
            log_info "内存使用: $memory"
        fi
        
    else
        log_warning "$APP_NAME服务未运行"
    fi
}

# 查看日志
show_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        tail -f "$LOG_FILE"
    else
        log_error "日志文件不存在: $LOG_FILE"
    fi
}

# 显示帮助
show_help() {
    echo "用法: $0 {start|stop|restart|status|logs}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动$APP_NAME服务"
    echo "  stop    - 停止$APP_NAME服务"
    echo "  restart - 重启$APP_NAME服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看实时日志"
    echo ""
}

# 主函数
main() {
    # 切换到脚本所在目录
    cd "$(dirname "$0")"
    
    # 检查环境
    check_java
    check_app_files
    
    # 处理命令
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
