jshERP 3.5 官方标准部署包使用说明
==========================================

1、文件说明
前端包/dist.zip                    前端压缩包
后端包/jshERP-bin.zip              后端压缩包
nginx配置文件-参考/nginx.conf      nginx配置参考文件
数据库/jsh_erp.sql                 数据库数据文件
数据库/init-database.sql           数据库初始化脚本
部署脚本/deploy.sh                 自动化部署脚本
部署脚本/start.sh                  服务启动脚本
管伊佳ERP-用户手册v3.5.pdf         用户操作手册

2、环境要求
操作系统: CentOS 7.5+ (推荐)
Java: JDK 1.8
数据库: MySQL 5.7+
缓存: Redis 6.0+
Web服务器: Nginx 1.16+
面板: 宝塔Linux面板 (推荐)

3、快速部署 (推荐)
# 解压部署包
tar -xzf jshERP3.5-最新包.tar.gz
cd jshERP3.5-最新包

# 执行自动化部署脚本
chmod +x 部署脚本/deploy.sh
sudo ./部署脚本/deploy.sh

# 按提示输入MySQL root密码即可完成部署

4、手动部署步骤

4.1 数据库配置
# 方式一：使用初始化脚本
mysql -u root -p < 数据库/init-database.sql

# 方式二：手动创建
mysql -u root -p
CREATE DATABASE jsh_erp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'jsh_user'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON jsh_erp.* TO 'jsh_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据
mysql -u jsh_user -p123456 jsh_erp < jsh_erp.sql

4.2 后端部署
# 创建目录
mkdir -p /home/<USER>/jshERP-boot
mkdir -p /opt/jshERP/{logs,upload}

# 解压后端包
cd /home/<USER>/jshERP-boot
unzip ../../jshERP3.5-最新包/后端包/jshERP-bin.zip

# 启动服务
chmod +x start.sh
./start.sh

4.3 前端部署
# 创建目录
mkdir -p /home/<USER>/jshERP-web

# 解压前端包
cd /home/<USER>/jshERP-web
unzip ../../jshERP3.5-最新包/前端包/dist.zip

4.4 Nginx配置
# 宝塔面板方式 (推荐)
1. 登录宝塔面板
2. 添加站点，域名填写服务器IP:3000
3. 在站点设置中配置反向代理
4. 参考 nginx配置文件-参考/nginx.conf

# 命令行方式
cp nginx配置文件-参考/nginx.conf /etc/nginx/nginx.conf
systemctl restart nginx

5、服务管理

5.1 启动服务
cd /home/<USER>/jshERP-boot
./start.sh

5.2 停止服务
./stop.sh

5.3 重启服务
./restart.sh

5.4 查看状态
./start.sh status

5.5 查看日志
./start.sh logs
# 或者
tail -f logs/jshERP.log

6、访问系统
前端地址: http://服务器IP:3000
后端地址: http://服务器IP:9999/jshERP-boot

默认账户:
- 管理员: admin / 123456
- 主账户: waterxi / 123456

7、常见问题

7.1 数据库连接失败
问题: Access denied for user 'jsh_user'@'localhost'
解决: 检查数据库用户是否创建，密码是否正确

7.2 端口被占用
问题: Port 9999 already in use
解决: 
netstat -tlnp | grep 9999
kill -9 <PID>

7.3 权限不足
问题: Permission denied
解决:
chown -R root:root /home/<USER>
chmod -R 755 /home/<USER>

7.4 内存不足
问题: OutOfMemoryError
解决: 修改启动脚本，增加JVM参数
java -Xms512m -Xmx1024m -jar lib/jshERP.jar

8、安全建议
1. 修改默认密码 (数据库、系统账户)
2. 配置防火墙，只开放必要端口
3. 定期备份数据库和上传文件
4. 启用HTTPS (生产环境)
5. 定期更新系统和软件

9、备份恢复
# 数据库备份
mysqldump -u jsh_user -p123456 jsh_erp > jsh_erp_backup.sql

# 文件备份
tar -czf jshERP_files_backup.tar.gz /opt/jshERP/upload

# 恢复
mysql -u jsh_user -p123456 jsh_erp < jsh_erp_backup.sql

10、技术支持
官方网站: https://gitee.com/jishenghua/JSH_ERP
文档地址: https://jishenghua.gitee.io/jsh_erp_doc
问题反馈: https://gitee.com/jishenghua/JSH_ERP/issues

构建信息:
构建时间: {BUILD_TIME}
构建版本: {BUILD_VERSION}
构建工具: jshERP标准化部署方案 v2.0
