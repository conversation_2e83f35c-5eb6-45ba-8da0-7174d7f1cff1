#!/bin/bash

# jshERP一键构建脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 一键构建完整的jshERP部署包，符合官方宝塔面板部署标准

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
DIST_DIR="$PROJECT_ROOT/dist"
DEPLOYMENT_DIR="$DIST_DIR/jshERP-deployment-package"

# 构建信息
BUILD_VERSION="3.5.0"
BUILD_DATE=$(date '+%Y-%m-%d %H:%M:%S')
BUILD_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示构建信息
show_build_info() {
    echo "=================================================="
    echo "    jshERP 一键构建脚本"
    echo "=================================================="
    echo "版本: $BUILD_VERSION"
    echo "构建时间: $BUILD_DATE"
    echo "Git Hash: $BUILD_HASH"
    echo "项目路径: $PROJECT_ROOT"
    echo "=================================================="
    echo ""
}

# 检查构建环境
check_build_environment() {
    log_step "检查构建环境..."
    
    # 检查必要的脚本文件
    local required_scripts=("build-frontend.sh" "build-backend.sh")
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SCRIPTS_DIR/$script" ]; then
            log_error "构建脚本不存在: $script"
            exit 1
        fi
        
        # 确保脚本可执行
        chmod +x "$SCRIPTS_DIR/$script"
    done
    
    # 检查项目结构
    if [ ! -d "$PROJECT_ROOT/jshERP-web" ]; then
        log_error "前端项目目录不存在"
        exit 1
    fi
    
    if [ ! -d "$PROJECT_ROOT/jshERP-boot" ]; then
        log_error "后端项目目录不存在"
        exit 1
    fi
    
    log_success "构建环境检查通过"
}

# 清理构建目录
clean_build_directory() {
    log_step "清理构建目录..."
    
    if [ -d "$DIST_DIR" ]; then
        rm -rf "$DIST_DIR"
        log_info "已清理旧的构建目录"
    fi
    
    # 创建构建目录结构
    mkdir -p "$DEPLOYMENT_DIR"/{frontend,backend,config,database,scripts,docs}
    
    log_success "构建目录准备完成"
}

# 构建前端
build_frontend() {
    log_step "构建前端项目..."
    
    cd "$SCRIPTS_DIR"
    
    # 执行前端构建脚本
    if [ "$1" = "--clean" ]; then
        ./build-frontend.sh --clean
    else
        ./build-frontend.sh
    fi
    
    # 检查构建结果
    if [ ! -f "$DIST_DIR/dist.zip" ]; then
        log_error "前端构建失败，dist.zip不存在"
        exit 1
    fi
    
    # 移动到部署目录
    mv "$DIST_DIR/dist.zip" "$DEPLOYMENT_DIR/frontend/"
    
    log_success "前端构建完成"
}

# 构建后端
build_backend() {
    log_step "构建后端项目..."
    
    cd "$SCRIPTS_DIR"
    
    # 执行后端构建脚本
    if [ "$1" = "--skip-tests" ]; then
        ./build-backend.sh --skip-tests
    else
        ./build-backend.sh
    fi
    
    # 检查构建结果
    if [ ! -f "$DIST_DIR/backend/jshERP-bin.zip" ]; then
        log_error "后端构建失败，jshERP-bin.zip不存在"
        exit 1
    fi
    
    # 移动到部署目录
    mv "$DIST_DIR/backend/jshERP-bin.zip" "$DEPLOYMENT_DIR/backend/"
    mv "$DIST_DIR/backend/application-production.properties" "$DEPLOYMENT_DIR/config/"
    
    log_success "后端构建完成"
}

# 创建配置文件
create_config_files() {
    log_step "创建配置文件..."
    
    # 创建Nginx配置文件
    cat > "$DEPLOYMENT_DIR/config/nginx-production.conf" << 'EOF'
# jshERP生产环境Nginx配置
# 基于官方部署标准

server {
    listen 3000;
    server_name localhost;
    
    # 启用gzip压缩
    gzip on;
    gzip_min_length 100;
    gzip_types text/plain text/css application/xml application/javascript;
    gzip_vary on;
    
    # 客户端上传文件大小限制
    client_max_body_size 10m;
    
    # 前端静态资源
    location / {
        root /home/<USER>/jshERP-web;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API代理
    location /jshERP-boot/ {
        proxy_pass http://localhost:9999/jshERP-boot/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
EOF
    
    # 创建环境变量模板
    cat > "$DEPLOYMENT_DIR/config/.env.template" << 'EOF'
# jshERP环境变量配置模板
# 复制此文件为.env并根据实际环境修改

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=jsh_erp
DB_USER=jsh_user
DB_PASSWORD=123456

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=1234abcd

# 应用配置
APP_PORT=9999
APP_CONTEXT_PATH=/jshERP-boot

# 文件上传配置
UPLOAD_PATH=/opt/jshERP/upload
TEMP_PATH=/opt/tmp/tomcat

# 日志配置
LOG_PATH=/opt/jshERP/logs
LOG_LEVEL=info

# 域名配置（可选）
DOMAIN_NAME=localhost
NGINX_PORT=3000
EOF
    
    log_success "配置文件创建完成"
}

# 准备数据库脚本
prepare_database() {
    log_step "准备数据库脚本..."
    
    # 复制数据库初始化脚本
    if [ -f "$PROJECT_ROOT/jshERP-boot/docs/jsh_erp.sql" ]; then
        cp "$PROJECT_ROOT/jshERP-boot/docs/jsh_erp.sql" "$DEPLOYMENT_DIR/database/jsh_erp_production.sql"
        log_info "已复制数据库初始化脚本"
    else
        log_warning "数据库初始化脚本不存在，请手动添加"
    fi
    
    log_success "数据库脚本准备完成"
}

# 创建部署脚本
create_deployment_scripts() {
    log_step "创建部署脚本..."
    
    # 创建部署脚本
    cat > "$DEPLOYMENT_DIR/scripts/deploy.sh" << 'EOF'
#!/bin/bash
# jshERP自动部署脚本

set -e

echo "开始部署jshERP..."

# 检查环境
if ! command -v unzip &> /dev/null; then
    echo "错误: unzip未安装"
    exit 1
fi

# 创建目录
sudo mkdir -p /home/<USER>/{jshERP-web,jshERP-boot}
sudo mkdir -p /opt/jshERP/{upload,logs}

# 部署前端
echo "部署前端..."
sudo unzip -o frontend/dist.zip -d /home/<USER>/jshERP-web/

# 部署后端
echo "部署后端..."
sudo unzip -o backend/jshERP-bin.zip -d /home/<USER>/jshERP-boot/

# 复制配置文件
echo "配置应用..."
sudo cp config/application-production.properties /home/<USER>/jshERP-boot/config/

# 设置权限
sudo chown -R www:www /home/<USER>/
sudo chmod +x /home/<USER>/jshERP-boot/*.sh

echo "部署完成！"
echo "请配置Nginx并启动服务"
EOF
    
    # 创建验证脚本
    cat > "$DEPLOYMENT_DIR/scripts/verify.sh" << 'EOF'
#!/bin/bash
# jshERP部署验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=================================================="
echo "    jshERP部署验证脚本"
echo "=================================================="

# 检查部署文件
log_info "检查部署文件..."
files=(
    "/home/<USER>/jshERP-web/index.html"
    "/home/<USER>/jshERP-boot/start.sh"
    "/home/<USER>/jshERP-boot/lib/jshERP.jar"
    "/home/<USER>/jshERP-boot/config/application-production.properties"
)

file_errors=0
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        log_success "✓ $file"
    else
        log_error "✗ $file 不存在"
        ((file_errors++))
    fi
done

# 检查目录权限
log_info "检查目录权限..."
dirs=(
    "/opt/jshERP/upload"
    "/opt/jshERP/logs"
    "/home/<USER>/jshERP-web"
    "/home/<USER>/jshERP-boot"
)

perm_errors=0
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ] && [ -w "$dir" ]; then
        log_success "✓ $dir (可写)"
    else
        log_error "✗ $dir (不存在或不可写)"
        ((perm_errors++))
    fi
done

# 检查服务状态
log_info "检查服务状态..."
if pgrep -f "jshERP.jar" > /dev/null; then
    log_success "✓ jshERP后端服务运行中"
else
    log_error "✗ jshERP后端服务未运行"
fi

if pgrep nginx > /dev/null; then
    log_success "✓ Nginx服务运行中"
else
    log_error "✗ Nginx服务未运行"
fi

# 检查端口监听
log_info "检查端口监听..."
if netstat -tlnp 2>/dev/null | grep -q ":9999 "; then
    log_success "✓ 后端端口9999监听中"
else
    log_error "✗ 后端端口9999未监听"
fi

if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
    log_success "✓ 前端端口3000监听中"
else
    log_error "✗ 前端端口3000未监听"
fi

# 总结
echo ""
echo "=================================================="
total_errors=$((file_errors + perm_errors))
if [ $total_errors -eq 0 ]; then
    log_success "部署验证通过！"
    echo "可以访问: http://localhost:3000"
else
    log_error "部署验证失败，发现 $total_errors 个问题"
    exit 1
fi
echo "=================================================="
EOF
    
    # 设置脚本权限
    chmod +x "$DEPLOYMENT_DIR/scripts"/*.sh
    
    log_success "部署脚本创建完成"
}

# 创建文档
create_documentation() {
    log_step "创建部署文档..."
    
    # 创建部署指南
    cat > "$DEPLOYMENT_DIR/docs/deployment-guide.md" << EOF
# jshERP标准化部署指南

## 构建信息
- 版本: $BUILD_VERSION
- 构建时间: $BUILD_DATE
- Git Hash: $BUILD_HASH

## 环境要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上（推荐8GB）
- 磁盘: 50GB以上可用空间
- 网络: 稳定的互联网连接

### 软件要求
- 操作系统: CentOS 7.5+ / Ubuntu 18.04+ (推荐CentOS 7.5)
- 面板: 宝塔Linux面板 7.0+
- 数据库: MySQL 5.7 (不支持8.0)
- Java: JDK 1.8 (通过宝塔面板安装Tomcat8获得)
- Web服务器: Nginx 1.16+
- 缓存: Redis 5.0+

## 部署前准备

### 1. 安装宝塔面板
\`\`\`bash
# CentOS 7安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
\`\`\`

### 2. 安装必要软件
在宝塔面板软件商店安装：
- Nginx 1.16+
- MySQL 5.7
- Redis 5.0+
- Tomcat 8 (用于获得JDK 1.8环境)

### 3. 配置防火墙
开放以下端口：
- 3000 (前端访问端口)
- 9999 (后端API端口，内网访问)
- 3306 (MySQL端口，内网访问)
- 6379 (Redis端口，内网访问)

## 详细部署步骤

### 第一步：上传部署包
1. 将jshERP-deployment-package-${BUILD_VERSION}.tar.gz上传到服务器
2. 解压部署包：
\`\`\`bash
cd /root
tar -xzf jshERP-deployment-package-${BUILD_VERSION}.tar.gz
cd jshERP-deployment-package
\`\`\`

### 第二步：配置生产环境
1. 运行配置生成脚本：
\`\`\`bash
chmod +x scripts/generate-config.sh
./scripts/generate-config.sh
\`\`\`

2. 根据提示输入生产环境配置信息：
   - 数据库连接信息
   - Redis连接信息
   - 域名和端口配置
   - 文件路径配置

### 第三步：初始化数据库
1. 在宝塔面板创建数据库：
   - 数据库名: jsh_erp
   - 用户名: jsh_user
   - 密码: 123456 (或自定义)

2. 导入数据库：
\`\`\`bash
mysql -u root -p < database/jsh_erp_production.sql
\`\`\`

### 第四步：部署应用文件
1. 执行自动部署脚本：
\`\`\`bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
\`\`\`

2. 手动部署（如果自动脚本失败）：
\`\`\`bash
# 创建目录
mkdir -p /home/<USER>/{jshERP-web,jshERP-boot}
mkdir -p /opt/jshERP/{upload,logs}

# 部署前端
unzip -o frontend/dist.zip -d /home/<USER>/jshERP-web/

# 部署后端
unzip -o backend/jshERP-bin.zip -d /home/<USER>/jshERP-boot/

# 复制配置文件
cp config/application-production.properties /home/<USER>/jshERP-boot/config/

# 设置权限
chown -R www:www /home/<USER>/
chown -R www:www /opt/jshERP/
chmod +x /home/<USER>/jshERP-boot/*.sh
\`\`\`

### 第五步：配置Nginx
1. 在宝塔面板添加站点：
   - 域名: 你的域名或IP
   - 端口: 3000
   - 根目录: /home/<USER>/jshERP-web

2. 配置Nginx：
\`\`\`bash
# 复制Nginx配置
cp config/nginx-production.conf /etc/nginx/conf.d/jshERP.conf

# 或者在宝塔面板中手动配置
\`\`\`

3. 重载Nginx配置：
\`\`\`bash
nginx -t
systemctl reload nginx
\`\`\`

### 第六步：启动应用服务
1. 启动jshERP后端：
\`\`\`bash
cd /home/<USER>/jshERP-boot
./start.sh
\`\`\`

2. 检查服务状态：
\`\`\`bash
./scripts/verify.sh
\`\`\`

### 第七步：验证部署
1. 访问系统：
   - 前端地址: http://your-domain:3000
   - 默认管理员账户: admin / 123456

2. 功能验证：
   - 登录系统
   - 检查菜单权限
   - 测试基本功能（商品管理、库存管理等）
   - 测试文件上传功能

## 生产环境优化

### 1. 性能优化
\`\`\`bash
# 调整JVM参数
vim /home/<USER>/jshERP-boot/start.sh
# 添加: -Xms1024m -Xmx2048m -XX:MetaspaceSize=256m

# 优化MySQL配置
vim /etc/mysql/mysql.conf.d/mysqld.cnf
# 添加:
# innodb_buffer_pool_size = 1G
# query_cache_size = 256M
\`\`\`

### 2. 安全配置
\`\`\`bash
# 修改默认密码
# 配置SSL证书（推荐）
# 设置防火墙规则
# 定期更新系统补丁
\`\`\`

### 3. 监控配置
\`\`\`bash
# 设置监控脚本
chmod +x scripts/monitor.sh
./scripts/monitor.sh --continuous 300

# 添加到系统服务
cp scripts/monitor.sh /usr/local/bin/
# 配置systemd服务或crontab
\`\`\`

## 维护和备份

### 1. 自动备份
\`\`\`bash
# 设置定时备份
crontab -e
# 添加: 0 2 * * * /path/to/database/backup-database.sh
\`\`\`

### 2. 日志管理
\`\`\`bash
# 配置日志轮转
vim /etc/logrotate.d/jshERP
\`\`\`

### 3. 定期维护
\`\`\`bash
# 每周执行数据库维护
0 3 * * 0 /path/to/database/maintenance.sh
\`\`\`

## 故障排除
详细的故障排除指南请参考 troubleshooting.md 文档

## 技术支持
如需技术支持，请提供：
1. 系统环境信息
2. 错误日志
3. 问题复现步骤
4. 监控报告

## 版本升级
升级新版本时：
1. 备份当前数据库和配置文件
2. 下载新版本部署包
3. 按照升级文档执行升级步骤
4. 验证升级结果

---
部署指南结束
EOF
    
    log_success "部署文档创建完成"
}

# 创建最终部署包
create_final_package() {
    log_step "创建最终部署包..."
    
    cd "$DIST_DIR"
    
    # 创建压缩包
    tar -czf "jshERP-deployment-package-${BUILD_VERSION}.tar.gz" jshERP-deployment-package/
    
    # 计算文件大小
    local package_size=$(du -sh "jshERP-deployment-package-${BUILD_VERSION}.tar.gz" | cut -f1)
    
    log_success "最终部署包创建完成"
    log_info "部署包大小: $package_size"
    log_info "部署包位置: $DIST_DIR/jshERP-deployment-package-${BUILD_VERSION}.tar.gz"
}

# 显示构建结果
show_build_result() {
    echo ""
    echo "=================================================="
    log_success "jshERP构建完成！"
    echo "=================================================="
    echo "构建信息:"
    echo "- 版本: $BUILD_VERSION"
    echo "- 构建时间: $BUILD_DATE"
    echo "- Git Hash: $BUILD_HASH"
    echo ""
    echo "构建产物:"
    echo "- 部署包: $DIST_DIR/jshERP-deployment-package-${BUILD_VERSION}.tar.gz"
    echo "- 源目录: $DEPLOYMENT_DIR"
    echo ""
    echo "下一步:"
    echo "1. 将部署包上传到服务器"
    echo "2. 解压并执行scripts/deploy.sh"
    echo "3. 按照docs/deployment-guide.md完成部署"
    echo "=================================================="
}

# 主函数
main() {
    # 解析参数
    local clean_frontend=false
    local skip_tests=false
    
    for arg in "$@"; do
        case $arg in
            --clean)
                clean_frontend=true
                ;;
            --skip-tests)
                skip_tests=true
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --clean       清理前端依赖后重新构建"
                echo "  --skip-tests  跳过后端测试"
                echo "  --help        显示此帮助信息"
                exit 0
                ;;
        esac
    done
    
    # 显示构建信息
    show_build_info
    
    # 执行构建流程
    check_build_environment
    clean_build_directory
    
    # 构建前端和后端
    if [ "$clean_frontend" = true ]; then
        build_frontend --clean
    else
        build_frontend
    fi
    
    if [ "$skip_tests" = true ]; then
        build_backend --skip-tests
    else
        build_backend
    fi
    
    # 创建配置和文档
    create_config_files
    prepare_database
    create_deployment_scripts
    create_documentation
    
    # 创建最终部署包
    create_final_package
    
    # 显示构建结果
    show_build_result
}

# 执行主函数
main "$@"
