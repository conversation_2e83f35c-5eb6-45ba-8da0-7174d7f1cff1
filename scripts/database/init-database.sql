-- jshERP数据库初始化脚本
-- 作者: Augment Code
-- 版本: 1.0
-- 用途: 自动创建jshERP所需的数据库和用户

-- ==========================================
-- 1. 创建数据库
-- ==========================================

-- 创建jshERP数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS jsh_erp 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'jshERP进销存管理系统数据库';

-- 显示创建结果
SELECT 'jsh_erp数据库创建成功' AS message;

-- ==========================================
-- 2. 创建用户和授权
-- ==========================================

-- 创建jsh_user用户（如果不存在）
-- 注意：生产环境请修改默认密码
CREATE USER IF NOT EXISTS 'jsh_user'@'localhost' IDENTIFIED BY '123456';

-- 授予jsh_user用户对jsh_erp数据库的完全权限
GRANT ALL PRIVILEGES ON jsh_erp.* TO 'jsh_user'@'localhost';

-- 刷新权限表
FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host FROM mysql.user WHERE User = 'jsh_user';

-- 显示授权信息
SHOW GRANTS FOR 'jsh_user'@'localhost';

-- ==========================================
-- 3. 验证数据库连接
-- ==========================================

-- 切换到jsh_erp数据库
USE jsh_erp;

-- 显示当前数据库
SELECT DATABASE() AS current_database;

-- ==========================================
-- 使用说明
-- ==========================================

/*
使用方法：

1. 命令行方式：
   mysql -u root -p < init-database.sql

2. 宝塔面板方式：
   - 登录宝塔面板
   - 进入数据库管理
   - 选择"导入"功能
   - 上传此文件并执行

3. 验证创建结果：
   mysql -u jsh_user -p123456 -e "SHOW DATABASES;"

注意事项：
- 请确保MySQL服务正在运行
- 生产环境请修改默认密码123456
- 如果用户已存在，此脚本会跳过创建步骤
- 执行前请备份现有数据
*/
