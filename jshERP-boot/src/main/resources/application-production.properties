# jshERP生产环境配置
server.port=9999
server.servlet.session.timeout=36000
server.servlet.context-path=/jshERP-boot

# 数据库连接（生产环境）
spring.datasource.url=******************************************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.username=jsh_user
spring.datasource.password=123456

# MyBatis配置
mybatis-plus.mapper-locations=classpath:./mapper_xml/*.xml

# Redis配置（生产环境）
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=1234abcd

# 租户配置
manage.roleId=10
tenant.userNumLimit=1000000
tenant.tryDayLimit=3000

# 文件上传配置（生产环境）
file.uploadType=1
file.path=/opt/jshERP/upload
server.tomcat.basedir=/opt/tmp/tomcat
spring.servlet.multipart.max-file-size=10485760
spring.servlet.multipart.max-request-size=10485760

# 日志配置（生产环境）
logging.level.com.jsh.erp=info
logging.level.root=warn
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=/opt/jshERP/logs/jshERP.log
logging.file.max-size=100MB
logging.file.max-history=30

# 性能优化配置
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
