package org.jeecg.modules.cloisonne.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.cloisonne.entity.CloisonneSchedule;
import org.jeecg.modules.cloisonne.service.IAIScheduleService;
import org.jeecg.modules.cloisonne.vo.AIScheduleConfigVO;
import org.jeecg.modules.cloisonne.vo.AIScheduleResultVO;
import org.jeecg.modules.cloisonne.vo.AIScheduleTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * AI智能排班控制器
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */
@Api(tags = "AI智能排班管理")
@RestController
@RequestMapping("/cloisonne/ai-schedule")
@Slf4j
public class AIScheduleController extends JeecgController<CloisonneSchedule, IAIScheduleService> {

    @Autowired
    private IAIScheduleService aiScheduleService;

    /**
     * 创建AI排班任务
     */
    @AutoLog(value = "AI排班-创建任务")
    @ApiOperation(value = "创建AI排班任务", notes = "创建AI排班任务")
    @PostMapping(value = "/generate")
    public Result<AIScheduleTaskVO> generateSchedule(@RequestBody AIScheduleConfigVO config, HttpServletRequest req) {
        try {
            log.info("创建AI排班任务，配置：{}", config);
            
            // 验证配置参数
            if (!aiScheduleService.validateConfig(config)) {
                return Result.error("配置参数验证失败");
            }
            
            // 创建排班任务
            AIScheduleTaskVO task = aiScheduleService.createScheduleTask(config);
            
            return Result.ok(task);
        } catch (Exception e) {
            log.error("创建AI排班任务失败", e);
            return Result.error("创建任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务进度
     */
    @AutoLog(value = "AI排班-查询进度")
    @ApiOperation(value = "查询任务进度", notes = "查询AI排班任务进度")
    @GetMapping(value = "/progress/{taskId}")
    public Result<JSONObject> getProgress(@PathVariable("taskId") String taskId) {
        try {
            JSONObject progress = aiScheduleService.getTaskProgress(taskId);
            return Result.ok(progress);
        } catch (Exception e) {
            log.error("查询任务进度失败", e);
            return Result.error("查询进度失败：" + e.getMessage());
        }
    }

    /**
     * 获取生成结果
     */
    @AutoLog(value = "AI排班-获取结果")
    @ApiOperation(value = "获取生成结果", notes = "获取AI排班生成结果")
    @GetMapping(value = "/result/{taskId}")
    public Result<AIScheduleResultVO> getResult(@PathVariable("taskId") String taskId) {
        try {
            AIScheduleResultVO result = aiScheduleService.getTaskResult(taskId);
            if (result == null) {
                return Result.error("任务结果不存在或尚未完成");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取任务结果失败", e);
            return Result.error("获取结果失败：" + e.getMessage());
        }
    }

    /**
     * 应用排班方案
     */
    @AutoLog(value = "AI排班-应用方案")
    @ApiOperation(value = "应用排班方案", notes = "应用AI生成的排班方案")
    @PostMapping(value = "/apply/{taskId}")
    public Result<String> applySchedule(@PathVariable("taskId") String taskId, HttpServletRequest req) {
        try {
            log.info("应用AI排班方案，任务ID：{}", taskId);
            
            boolean success = aiScheduleService.applyScheduleResult(taskId);
            if (success) {
                return Result.ok("排班方案应用成功");
            } else {
                return Result.error("排班方案应用失败");
            }
        } catch (Exception e) {
            log.error("应用排班方案失败", e);
            return Result.error("应用失败：" + e.getMessage());
        }
    }

    /**
     * 调整排班方案
     */
    @AutoLog(value = "AI排班-调整方案")
    @ApiOperation(value = "调整排班方案", notes = "手动调整AI生成的排班方案")
    @PutMapping(value = "/adjust/{taskId}")
    public Result<AIScheduleResultVO> adjustSchedule(
            @PathVariable("taskId") String taskId,
            @RequestBody List<Map<String, Object>> adjustments) {
        try {
            log.info("调整AI排班方案，任务ID：{}，调整项：{}", taskId, adjustments.size());
            
            AIScheduleResultVO result = aiScheduleService.adjustSchedule(taskId, adjustments);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("调整排班方案失败", e);
            return Result.error("调整失败：" + e.getMessage());
        }
    }

    /**
     * 停止生成任务
     */
    @AutoLog(value = "AI排班-停止任务")
    @ApiOperation(value = "停止生成任务", notes = "停止正在运行的AI排班任务")
    @PostMapping(value = "/stop/{taskId}")
    public Result<String> stopTask(@PathVariable("taskId") String taskId) {
        try {
            boolean success = aiScheduleService.stopTask(taskId);
            if (success) {
                return Result.ok("任务已停止");
            } else {
                return Result.error("停止任务失败");
            }
        } catch (Exception e) {
            log.error("停止任务失败", e);
            return Result.error("停止失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务列表
     */
    @AutoLog(value = "AI排班-任务列表")
    @ApiOperation(value = "获取任务列表", notes = "获取AI排班任务列表")
    @GetMapping(value = "/tasks")
    public Result<List<AIScheduleTaskVO>> getTasks(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            List<AIScheduleTaskVO> tasks = aiScheduleService.getTaskList(status, pageNo, pageSize);
            return Result.ok(tasks);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return Result.error("获取列表失败：" + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @AutoLog(value = "AI排班-删除任务")
    @ApiOperation(value = "删除任务", notes = "删除AI排班任务")
    @DeleteMapping(value = "/delete/{taskId}")
    public Result<String> deleteTask(@PathVariable("taskId") String taskId) {
        try {
            boolean success = aiScheduleService.deleteTask(taskId);
            if (success) {
                return Result.ok("任务已删除");
            } else {
                return Result.error("删除任务失败");
            }
        } catch (Exception e) {
            log.error("删除任务失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取配置模板
     */
    @AutoLog(value = "AI排班-配置模板")
    @ApiOperation(value = "获取配置模板", notes = "获取AI排班配置模板")
    @GetMapping(value = "/templates")
    public Result<List<AIScheduleConfigVO>> getConfigTemplates() {
        try {
            List<AIScheduleConfigVO> templates = aiScheduleService.getConfigTemplates();
            return Result.ok(templates);
        } catch (Exception e) {
            log.error("获取配置模板失败", e);
            return Result.error("获取模板失败：" + e.getMessage());
        }
    }

    /**
     * 保存配置模板
     */
    @AutoLog(value = "AI排班-保存模板")
    @ApiOperation(value = "保存配置模板", notes = "保存AI排班配置模板")
    @PostMapping(value = "/templates")
    public Result<String> saveConfigTemplate(@RequestBody AIScheduleConfigVO config) {
        try {
            boolean success = aiScheduleService.saveConfigTemplate(config);
            if (success) {
                return Result.ok("模板保存成功");
            } else {
                return Result.error("模板保存失败");
            }
        } catch (Exception e) {
            log.error("保存配置模板失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取算法统计
     */
    @AutoLog(value = "AI排班-算法统计")
    @ApiOperation(value = "获取算法统计", notes = "获取AI排班算法统计信息")
    @GetMapping(value = "/statistics")
    public Result<JSONObject> getStatistics(
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            JSONObject statistics = aiScheduleService.getAlgorithmStatistics(startDate, endDate);
            return Result.ok(statistics);
        } catch (Exception e) {
            log.error("获取算法统计失败", e);
            return Result.error("获取统计失败：" + e.getMessage());
        }
    }
}
