package org.jeecg.modules.cloisonne.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AI排班结果VO
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AIScheduleResultVO", description = "AI排班结果")
public class AIScheduleResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "排班数据")
    private Map<String, Map<String, List<EmployeeSchedule>>> schedule;

    @ApiModelProperty(value = "优化指标")
    private OptimizationMetrics metrics;

    @ApiModelProperty(value = "适应度分数")
    private Double fitnessScore;

    @ApiModelProperty(value = "生成代数")
    private Integer generations;

    @ApiModelProperty(value = "优化建议")
    private List<OptimizationSuggestion> suggestions;

    @ApiModelProperty(value = "统计信息")
    private ScheduleStatistics statistics;

    @ApiModelProperty(value = "冲突信息")
    private List<ConflictInfo> conflicts;

    @ApiModelProperty(value = "生成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date generateTime;

    @ApiModelProperty(value = "是否已应用")
    private Boolean applied;

    @ApiModelProperty(value = "应用时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 员工排班信息
     */
    @Data
    @ApiModel(value = "EmployeeSchedule", description = "员工排班信息")
    public static class EmployeeSchedule implements Serializable {
        @ApiModelProperty(value = "员工ID")
        private Long employeeId;

        @ApiModelProperty(value = "员工姓名")
        private String employeeName;

        @ApiModelProperty(value = "班次类型")
        private String shiftType;

        @ApiModelProperty(value = "开始时间")
        private String startTime;

        @ApiModelProperty(value = "结束时间")
        private String endTime;

        @ApiModelProperty(value = "工作时长")
        private Double workHours;

        @ApiModelProperty(value = "工作区域")
        private String workArea;

        @ApiModelProperty(value = "优先级")
        private String priority;

        @ApiModelProperty(value = "备注")
        private String notes;
    }

    /**
     * 优化指标
     */
    @Data
    @ApiModel(value = "OptimizationMetrics", description = "优化指标")
    public static class OptimizationMetrics implements Serializable {
        @ApiModelProperty(value = "覆盖率")
        private Double coverage;

        @ApiModelProperty(value = "公平性")
        private Double fairness;

        @ApiModelProperty(value = "满意度")
        private Double satisfaction;

        @ApiModelProperty(value = "成本效率")
        private Double costEfficiency;

        @ApiModelProperty(value = "综合评分")
        private Double overallScore;
    }

    /**
     * 优化建议
     */
    @Data
    @ApiModel(value = "OptimizationSuggestion", description = "优化建议")
    public static class OptimizationSuggestion implements Serializable {
        @ApiModelProperty(value = "建议类型")
        private String type;

        @ApiModelProperty(value = "建议标题")
        private String title;

        @ApiModelProperty(value = "建议描述")
        private String description;

        @ApiModelProperty(value = "优先级")
        private String priority;

        @ApiModelProperty(value = "是否可操作")
        private Boolean actionable;

        @ApiModelProperty(value = "影响范围")
        private List<String> affectedItems;

        @ApiModelProperty(value = "预期改善")
        private Map<String, Double> expectedImprovement;

        @ApiModelProperty(value = "操作步骤")
        private List<String> actionSteps;
    }

    /**
     * 排班统计
     */
    @Data
    @ApiModel(value = "ScheduleStatistics", description = "排班统计")
    public static class ScheduleStatistics implements Serializable {
        @ApiModelProperty(value = "总排班数")
        private Integer totalSchedules;

        @ApiModelProperty(value = "参与员工数")
        private Integer participatingEmployees;

        @ApiModelProperty(value = "平均工作时长")
        private Double averageWorkHours;

        @ApiModelProperty(value = "最大工作时长")
        private Double maxWorkHours;

        @ApiModelProperty(value = "最小工作时长")
        private Double minWorkHours;

        @ApiModelProperty(value = "班次分布")
        private Map<String, Integer> shiftDistribution;

        @ApiModelProperty(value = "员工工作统计")
        private Map<String, EmployeeWorkStats> employeeStats;
    }

    /**
     * 员工工作统计
     */
    @Data
    @ApiModel(value = "EmployeeWorkStats", description = "员工工作统计")
    public static class EmployeeWorkStats implements Serializable {
        @ApiModelProperty(value = "员工ID")
        private Long employeeId;

        @ApiModelProperty(value = "员工姓名")
        private String employeeName;

        @ApiModelProperty(value = "工作天数")
        private Integer workDays;

        @ApiModelProperty(value = "总工作时长")
        private Double totalHours;

        @ApiModelProperty(value = "平均每日工时")
        private Double averageDailyHours;

        @ApiModelProperty(value = "班次分布")
        private Map<String, Integer> shiftBreakdown;

        @ApiModelProperty(value = "偏好满足度")
        private Double preferenceSatisfaction;
    }

    /**
     * 冲突信息
     */
    @Data
    @ApiModel(value = "ConflictInfo", description = "冲突信息")
    public static class ConflictInfo implements Serializable {
        @ApiModelProperty(value = "冲突类型")
        private String conflictType;

        @ApiModelProperty(value = "冲突描述")
        private String description;

        @ApiModelProperty(value = "严重程度")
        private String severity;

        @ApiModelProperty(value = "涉及员工")
        private List<String> involvedEmployees;

        @ApiModelProperty(value = "涉及日期")
        private String involvedDate;

        @ApiModelProperty(value = "涉及班次")
        private String involvedShift;

        @ApiModelProperty(value = "解决建议")
        private String resolution;
    }

    /**
     * 冲突类型枚举
     */
    public static class ConflictType {
        public static final String TIME_OVERLAP = "time_overlap";           // 时间重叠
        public static final String AVAILABILITY = "availability";           // 可用性冲突
        public static final String SKILL_MISMATCH = "skill_mismatch";       // 技能不匹配
        public static final String WORKLOAD_EXCESS = "workload_excess";     // 工作量超标
        public static final String PREFERENCE_VIOLATION = "preference_violation"; // 偏好违反
        public static final String COVERAGE_INSUFFICIENT = "coverage_insufficient"; // 覆盖不足
    }

    /**
     * 严重程度枚举
     */
    public static class Severity {
        public static final String LOW = "low";        // 低
        public static final String MEDIUM = "medium";  // 中
        public static final String HIGH = "high";      // 高
        public static final String CRITICAL = "critical"; // 严重
    }
}
