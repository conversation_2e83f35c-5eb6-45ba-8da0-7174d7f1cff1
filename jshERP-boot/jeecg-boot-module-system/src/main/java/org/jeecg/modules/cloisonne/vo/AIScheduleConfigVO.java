package org.jeecg.modules.cloisonne.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AI排班配置VO
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AIScheduleConfigVO", description = "AI排班配置")
public class AIScheduleConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置ID")
    private String configId;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty(value = "参与员工ID列表")
    private List<Long> employeeIds;

    @ApiModelProperty(value = "算法类型")
    private String algorithm;

    @ApiModelProperty(value = "优化目标权重")
    private ObjectiveWeights objectives;

    @ApiModelProperty(value = "约束条件")
    private ScheduleConstraints constraints;

    @ApiModelProperty(value = "算法参数")
    private AlgorithmParameters algorithmParams;

    @ApiModelProperty(value = "是否为模板")
    private Boolean isTemplate;

    @ApiModelProperty(value = "模板描述")
    private String templateDescription;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建用户")
    private String createUser;

    /**
     * 优化目标权重
     */
    @Data
    @ApiModel(value = "ObjectiveWeights", description = "优化目标权重")
    public static class ObjectiveWeights implements Serializable {
        @ApiModelProperty(value = "覆盖率权重")
        private Integer coverage;

        @ApiModelProperty(value = "公平性权重")
        private Integer fairness;

        @ApiModelProperty(value = "满意度权重")
        private Integer satisfaction;

        @ApiModelProperty(value = "成本效率权重")
        private Integer costEfficiency;
    }

    /**
     * 排班约束条件
     */
    @Data
    @ApiModel(value = "ScheduleConstraints", description = "排班约束条件")
    public static class ScheduleConstraints implements Serializable {
        @ApiModelProperty(value = "员工约束")
        private EmployeeConstraints employee;

        @ApiModelProperty(value = "时间约束")
        private TimeConstraints time;

        @ApiModelProperty(value = "业务约束")
        private BusinessConstraints business;
    }

    /**
     * 员工约束
     */
    @Data
    @ApiModel(value = "EmployeeConstraints", description = "员工约束")
    public static class EmployeeConstraints implements Serializable {
        @ApiModelProperty(value = "员工可用时间")
        private Map<Long, Map<String, AvailabilityInfo>> availability;

        @ApiModelProperty(value = "员工偏好")
        private Map<Long, PreferenceInfo> preferences;

        @ApiModelProperty(value = "员工技能")
        private Map<Long, List<String>> skills;
    }

    /**
     * 可用时间信息
     */
    @Data
    @ApiModel(value = "AvailabilityInfo", description = "可用时间信息")
    public static class AvailabilityInfo implements Serializable {
        @ApiModelProperty(value = "是否可用")
        private Boolean available;

        @ApiModelProperty(value = "开始时间")
        private String startTime;

        @ApiModelProperty(value = "结束时间")
        private String endTime;
    }

    /**
     * 偏好信息
     */
    @Data
    @ApiModel(value = "PreferenceInfo", description = "偏好信息")
    public static class PreferenceInfo implements Serializable {
        @ApiModelProperty(value = "偏好班次")
        private List<String> preferredShifts;

        @ApiModelProperty(value = "避免班次")
        private List<String> avoidShifts;

        @ApiModelProperty(value = "最大连续天数")
        private Integer maxConsecutiveDays;

        @ApiModelProperty(value = "偏好休息日")
        private List<String> preferredDaysOff;
    }

    /**
     * 时间约束
     */
    @Data
    @ApiModel(value = "TimeConstraints", description = "时间约束")
    public static class TimeConstraints implements Serializable {
        @ApiModelProperty(value = "每班次最少人数")
        private Map<String, Integer> minStaffPerShift;

        @ApiModelProperty(value = "每班次最多人数")
        private Map<String, Integer> maxStaffPerShift;

        @ApiModelProperty(value = "必须覆盖时段")
        private Map<String, CoverageRequirement> requiredCoverage;
    }

    /**
     * 覆盖要求
     */
    @Data
    @ApiModel(value = "CoverageRequirement", description = "覆盖要求")
    public static class CoverageRequirement implements Serializable {
        @ApiModelProperty(value = "是否必须覆盖")
        private Boolean required;

        @ApiModelProperty(value = "开始时间")
        private String startTime;

        @ApiModelProperty(value = "结束时间")
        private String endTime;

        @ApiModelProperty(value = "最少人数")
        private Integer minStaff;
    }

    /**
     * 业务约束
     */
    @Data
    @ApiModel(value = "BusinessConstraints", description = "业务约束")
    public static class BusinessConstraints implements Serializable {
        @ApiModelProperty(value = "特殊事件")
        private List<SpecialEvent> specialEvents;

        @ApiModelProperty(value = "节假日安排")
        private List<HolidayArrangement> holidays;
    }

    /**
     * 特殊事件
     */
    @Data
    @ApiModel(value = "SpecialEvent", description = "特殊事件")
    public static class SpecialEvent implements Serializable {
        @ApiModelProperty(value = "日期")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        private Date date;

        @ApiModelProperty(value = "所需人数")
        private Integer requiredStaff;

        @ApiModelProperty(value = "所需技能")
        private List<String> requiredSkills;

        @ApiModelProperty(value = "事件描述")
        private String description;
    }

    /**
     * 节假日安排
     */
    @Data
    @ApiModel(value = "HolidayArrangement", description = "节假日安排")
    public static class HolidayArrangement implements Serializable {
        @ApiModelProperty(value = "日期")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        private Date date;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "最少人数")
        private Integer minStaff;

        @ApiModelProperty(value = "描述")
        private String description;
    }

    /**
     * 算法参数
     */
    @Data
    @ApiModel(value = "AlgorithmParameters", description = "算法参数")
    public static class AlgorithmParameters implements Serializable {
        @ApiModelProperty(value = "最大迭代次数")
        private Integer maxIterations;

        @ApiModelProperty(value = "收敛阈值")
        private Double convergenceThreshold;

        @ApiModelProperty(value = "种群大小")
        private Integer populationSize;

        @ApiModelProperty(value = "变异率")
        private Double mutationRate;

        @ApiModelProperty(value = "交叉率")
        private Double crossoverRate;

        @ApiModelProperty(value = "精英大小")
        private Integer eliteSize;
    }
}
