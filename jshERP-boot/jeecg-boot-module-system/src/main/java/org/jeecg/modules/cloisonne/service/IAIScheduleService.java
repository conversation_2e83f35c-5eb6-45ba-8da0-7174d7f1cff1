package org.jeecg.modules.cloisonne.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cloisonne.entity.CloisonneSchedule;
import org.jeecg.modules.cloisonne.vo.AIScheduleConfigVO;
import org.jeecg.modules.cloisonne.vo.AIScheduleResultVO;
import org.jeecg.modules.cloisonne.vo.AIScheduleTaskVO;

import java.util.List;
import java.util.Map;

/**
 * AI智能排班服务接口
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */
public interface IAIScheduleService extends IService<CloisonneSchedule> {

    /**
     * 验证配置参数
     * 
     * @param config 配置参数
     * @return 验证结果
     */
    boolean validateConfig(AIScheduleConfigVO config);

    /**
     * 创建排班任务
     * 
     * @param config 配置参数
     * @return 任务信息
     */
    AIScheduleTaskVO createScheduleTask(AIScheduleConfigVO config);

    /**
     * 获取任务进度
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    JSONObject getTaskProgress(String taskId);

    /**
     * 获取任务结果
     * 
     * @param taskId 任务ID
     * @return 结果信息
     */
    AIScheduleResultVO getTaskResult(String taskId);

    /**
     * 应用排班结果
     * 
     * @param taskId 任务ID
     * @return 应用结果
     */
    boolean applyScheduleResult(String taskId);

    /**
     * 调整排班方案
     * 
     * @param taskId 任务ID
     * @param adjustments 调整项
     * @return 调整后的结果
     */
    AIScheduleResultVO adjustSchedule(String taskId, List<Map<String, Object>> adjustments);

    /**
     * 停止任务
     * 
     * @param taskId 任务ID
     * @return 停止结果
     */
    boolean stopTask(String taskId);

    /**
     * 获取任务列表
     * 
     * @param status 状态筛选
     * @param pageNo 页码
     * @param pageSize 页面大小
     * @return 任务列表
     */
    List<AIScheduleTaskVO> getTaskList(String status, Integer pageNo, Integer pageSize);

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    boolean deleteTask(String taskId);

    /**
     * 获取配置模板
     * 
     * @return 模板列表
     */
    List<AIScheduleConfigVO> getConfigTemplates();

    /**
     * 保存配置模板
     * 
     * @param config 配置信息
     * @return 保存结果
     */
    boolean saveConfigTemplate(AIScheduleConfigVO config);

    /**
     * 获取算法统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    JSONObject getAlgorithmStatistics(String startDate, String endDate);

    /**
     * 执行AI排班算法
     * 
     * @param taskId 任务ID
     * @param config 配置参数
     */
    void executeScheduleAlgorithm(String taskId, AIScheduleConfigVO config);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param progress 进度信息
     */
    void updateTaskProgress(String taskId, JSONObject progress);

    /**
     * 保存任务结果
     * 
     * @param taskId 任务ID
     * @param result 结果信息
     */
    void saveTaskResult(String taskId, AIScheduleResultVO result);

    /**
     * 获取员工可用性
     * 
     * @param employeeIds 员工ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 可用性信息
     */
    Map<String, Object> getEmployeeAvailability(List<Long> employeeIds, String startDate, String endDate);

    /**
     * 检查排班冲突
     * 
     * @param scheduleData 排班数据
     * @return 冲突信息
     */
    List<Map<String, Object>> checkScheduleConflicts(Map<String, Object> scheduleData);

    /**
     * 计算排班指标
     * 
     * @param scheduleData 排班数据
     * @return 指标信息
     */
    Map<String, Object> calculateScheduleMetrics(Map<String, Object> scheduleData);

    /**
     * 生成优化建议
     * 
     * @param scheduleData 排班数据
     * @param metrics 指标信息
     * @return 建议列表
     */
    List<Map<String, Object>> generateOptimizationSuggestions(Map<String, Object> scheduleData, Map<String, Object> metrics);

    /**
     * 导出排班结果
     * 
     * @param taskId 任务ID
     * @param format 导出格式
     * @return 导出文件路径
     */
    String exportScheduleResult(String taskId, String format);

    /**
     * 获取算法配置
     * 
     * @return 算法配置
     */
    Map<String, Object> getAlgorithmConfig();

    /**
     * 更新算法配置
     * 
     * @param config 配置信息
     * @return 更新结果
     */
    boolean updateAlgorithmConfig(Map<String, Object> config);
}
