package org.jeecg.modules.cloisonne.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * AI排班任务VO
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AIScheduleTaskVO", description = "AI排班任务")
public class AIScheduleTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务状态")
    private String status;

    @ApiModelProperty(value = "任务进度")
    private Integer progress;

    @ApiModelProperty(value = "当前阶段")
    private String currentStage;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "已用时间(秒)")
    private Long elapsedTime;

    @ApiModelProperty(value = "预计剩余时间(秒)")
    private Long estimatedRemainingTime;

    @ApiModelProperty(value = "当前代数")
    private Integer currentGeneration;

    @ApiModelProperty(value = "最大代数")
    private Integer maxGenerations;

    @ApiModelProperty(value = "最优适应度")
    private Double bestFitness;

    @ApiModelProperty(value = "平均适应度")
    private Double averageFitness;

    @ApiModelProperty(value = "配置信息")
    private AIScheduleConfigVO config;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "创建用户")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 任务状态枚举
     */
    public static class TaskStatus {
        public static final String PENDING = "pending";      // 等待中
        public static final String RUNNING = "running";      // 运行中
        public static final String COMPLETED = "completed";  // 已完成
        public static final String FAILED = "failed";        // 失败
        public static final String CANCELLED = "cancelled";  // 已取消
        public static final String STOPPED = "stopped";      // 已停止
    }

    /**
     * 任务阶段枚举
     */
    public static class TaskStage {
        public static final String INITIALIZING = "initializing";    // 初始化
        public static final String VALIDATING = "validating";        // 验证配置
        public static final String GENERATING = "generating";        // 生成种群
        public static final String EVALUATING = "evaluating";        // 评估适应度
        public static final String EVOLVING = "evolving";            // 进化操作
        public static final String OPTIMIZING = "optimizing";        // 优化解
        public static final String FINALIZING = "finalizing";        // 完成处理
        public static final String COMPLETED = "completed";          // 已完成
    }
}
