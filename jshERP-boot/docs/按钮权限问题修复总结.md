# jshERP 按钮权限问题修复总结

## 问题概述

在删除薪酬管理模块后，系统出现了两个关键问题：
1. **左侧导航栏消失** - 用户权限配置被误删
2. **功能按钮消失** - 按钮权限配置格式错误

## 问题根源分析

### 1. 导航栏消失问题

**根本原因**：数据库中权限配置的type字段值不匹配
- ❌ 数据库存储：`type = 'RoleFunction'`
- ✅ 后端代码查找：`type = 'RoleFunctions'` (多了一个s)

**影响链路**：
```
用户登录 → 获取权限 → 查询RoleFunctions → 查询结果为空 → 菜单数据为空 → 导航栏空白
```

### 2. 功能按钮消失问题

**根本原因**：前端按钮权限存储和读取位置不一致
- ❌ 存储位置：`Vue.ls.set('winBtnStrList', res.data.userBtn)`
- ❌ 读取位置：`sessionStorage.getItem(USER_AUTH)`

**影响链路**：
```
权限获取 → 存储到localStorage → 页面读取sessionStorage → 读取失败 → 按钮权限验证失败 → 所有按钮隐藏
```

## 修复方案

### 1. 数据库权限修复

```sql
-- 修复权限配置的type字段
UPDATE jsh_user_business 
SET type = 'RoleFunctions' 
WHERE type = 'RoleFunction' AND key_id = '10';

-- 扩展btn_str字段长度
ALTER TABLE jsh_user_business MODIFY COLUMN btn_str TEXT;
```

### 2. 前端权限机制修复

**修改文件**：`jshERP-web/src/permission.js`

```javascript
// 添加USER_AUTH导入
import { USER_ID,INDEX_MAIN_PAGE_PATH,USER_AUTH } from '@/store/mutation-types'

// 修改按钮权限缓存逻辑
store.dispatch('GetUserBtnList').then(res => {
  Vue.ls.set('winBtnStrList', res.data.userBtn, 7 * 24 * 60 * 60 * 1000)
  // 同时设置到sessionStorage中供按钮权限验证使用
  if(res.data.userBtn) {
    sessionStorage.setItem(USER_AUTH, JSON.stringify(res.data.userBtn))
  }
})
```

### 3. 完整按钮权限配置

为角色10配置所有79个功能模块的完整按钮权限，包括：
- 系统管理模块 (11个功能)
- 业务管理模块 (35个功能) 
- 专业功能模块 (20个功能)
- 珐琅馆专属模块 (13个功能)

## 技术要点

### 1. 权限数据格式

**正确的按钮权限格式**：
```json
[
  {"funId":210,"btnStr":"1,2,3,4,7"},  // 零售出库
  {"funId":241,"btnStr":"1,2,3,7"},    // 采购订单(特殊)
  {"funId":211,"btnStr":"1,2,3,4,7"}   // 零售退货
]
```

**按钮权限说明**：
- 1 - 新增权限
- 2 - 修改权限
- 3 - 删除权限
- 4 - 查看权限
- 7 - 导入导出权限

### 2. 特殊模块处理

某些模块的按钮权限配置与标准不同：
- **采购订单** - 只支持 1,2,3,7 (无查看权限)
- **其他模块** - 支持 1,2,3,4,7 (完整权限)

### 3. 权限验证机制

```
数据获取 → 后端API返回用户按钮权限
数据存储 → 前端存储到localStorage和sessionStorage
权限验证 → 页面组件通过v-has指令验证权限
按钮显示 → 根据权限结果显示或隐藏按钮
```

## 预防措施

1. **删除模块时的检查清单**：
   - 检查是否影响用户权限配置
   - 检查是否影响角色功能权限
   - 检查前端路由和权限文件
   - 验证删除后的系统功能

2. **权限配置备份**：
   - 定期备份jsh_user_business表
   - 记录重要权限配置的SQL语句
   - 保存权限恢复脚本

3. **测试验证**：
   - 删除操作后立即测试登录
   - 验证导航栏和功能按钮
   - 检查各个模块的权限

## 故障排查步骤

1. **导航栏问题**：
   - 检查用户角色关系
   - 检查角色功能权限
   - 验证type字段值匹配

2. **按钮权限问题**：
   - 检查前端权限存储
   - 验证sessionStorage数据
   - 检查按钮权限格式

3. **特定模块问题**：
   - 检查功能ID配置
   - 验证push_btn字段
   - 确认权限格式匹配

## 相关文件

- **后端权限查询**：`FunctionController.java`
- **前端权限处理**：`permission.js`
- **权限验证**：`hasPermission.js`
- **数据库表**：`jsh_user_business`、`jsh_function`
- **恢复脚本**：`restore_button_permissions.sql`
