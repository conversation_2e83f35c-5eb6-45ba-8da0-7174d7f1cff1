# jshERP 盘点业务模块快速部署指南

## 🚀 5分钟快速启动

### 前置条件检查

确保您的环境满足以下要求：
- ✅ JDK 1.8+ 已安装
- ✅ MySQL 5.7+ 运行中
- ✅ Node.js 14+ 已安装
- ✅ jshERP 基础系统已部署

### 第一步：数据库准备

#### 1.1 检查数据库表
```sql
-- 连接到 jsh_erp 数据库
USE jsh_erp;

-- 检查盘点相关表是否存在
SHOW TABLES LIKE '%inventory_check%';

-- 应该看到以下表：
-- jsh_inventory_check
-- jsh_inventory_check_detail
-- jsh_inventory_check_config
-- jsh_inventory_check_item
```

#### 1.2 如果表不存在，创建表结构
```sql
-- 如果需要，可以从现有的 SQL 文件导入表结构
-- 或者联系管理员获取建表脚本
```

### 第二步：后端部署

#### 2.1 编译项目
```bash
# 进入后端项目目录
cd jshERP-boot

# 清理并编译
mvn clean compile

# 如果编译成功，继续下一步
```

#### 2.2 启动后端服务
```bash
# 方式一：使用 Maven 启动（开发环境）
mvn spring-boot:run

# 方式二：打包后启动（生产环境）
mvn clean package -DskipTests
java -jar target/jshERP-boot-*.jar

# 等待启动完成，看到以下信息表示成功：
# Started JshErpApplication in xxx seconds
```

#### 2.3 验证后端服务
```bash
# 测试健康检查接口
curl http://localhost:9999/health

# 测试盘点接口
curl http://localhost:9999/inventoryCheck/statistics
```

### 第三步：前端部署

#### 3.1 安装依赖
```bash
# 进入前端项目目录
cd jshERP-web

# 安装依赖（如果之前没有安装）
npm install

# 如果安装缓慢，可以使用国内镜像
npm install --registry=https://registry.npm.taobao.org
```

#### 3.2 启动前端服务
```bash
# 启动开发服务器
npm run serve

# 等待启动完成，看到以下信息：
# App running at:
# - Local:   http://localhost:8080/
# - Network: http://192.168.x.x:8080/
```

#### 3.3 验证前端服务
```bash
# 浏览器访问
open http://localhost:8080

# 或者使用 curl 测试
curl http://localhost:8080
```

### 第四步：权限配置

#### 4.1 执行权限恢复脚本
```bash
# 进入项目根目录
cd /path/to/jshERP

# 执行一键权限恢复脚本
./jshERP-boot/scripts/restore_all_permissions.sh

# 按提示输入数据库信息：
# 数据库主机: localhost
# 数据库端口: 3306
# 数据库名称: jsh_erp
# 数据库用户: jsh_user
# 数据库密码: [您的密码]
```

#### 4.2 手动权限配置（如果脚本失败）
```sql
-- 连接数据库
mysql -u jsh_user -p jsh_erp

-- 执行权限配置 SQL
source jshERP-boot/scripts/restore_button_permissions.sql;
```

#### 4.3 前端权限修复
```bash
# 执行前端权限修复脚本
node jshERP-boot/scripts/fix_frontend_permissions.js
```

### 第五步：验证部署

#### 5.1 登录系统
1. 浏览器访问：http://localhost:8080
2. 使用管理员账户登录：
   - 用户名：管理员
   - 密码：[您设置的密码]

#### 5.2 检查盘点模块
1. 查看左侧导航栏是否显示"库存管理"菜单
2. 点击"库存管理" → "库存盘点" → "盘点列表"
3. 确认页面正常加载，显示盘点列表界面

#### 5.3 功能测试
1. **创建盘点单**：
   - 点击"新增盘点"按钮
   - 填写盘点信息
   - 保存成功

2. **开始盘点**：
   - 在列表中找到创建的盘点单
   - 点击"开始盘点"
   - 确认状态变为"盘点中"

3. **录入数据**：
   - 点击"录入数据"按钮
   - 进入盘点录入页面
   - 测试数据录入功能

4. **查看详情**：
   - 完成盘点后查看详情
   - 确认统计数据和图表正常显示

## 🔧 常见问题解决

### 问题1：后端启动失败

**症状**：后端服务无法启动，报错连接数据库失败

**解决方案**：
```bash
# 1. 检查数据库服务是否启动
systemctl status mysql
# 或
brew services list | grep mysql

# 2. 检查数据库连接配置
cat jshERP-boot/src/main/resources/application.yml

# 3. 测试数据库连接
mysql -h localhost -u jsh_user -p jsh_erp
```

### 问题2：前端页面空白

**症状**：前端页面加载后显示空白或报错

**解决方案**：
```bash
# 1. 清除浏览器缓存
# 按 Ctrl+Shift+Delete，清除所有缓存

# 2. 检查控制台错误
# 按 F12 打开开发者工具，查看 Console 错误

# 3. 重新安装依赖
cd jshERP-web
rm -rf node_modules package-lock.json
npm install
npm run serve
```

### 问题3：权限按钮不显示

**症状**：登录后看不到功能按钮或菜单

**解决方案**：
```bash
# 1. 重新执行权限恢复脚本
./jshERP-boot/scripts/restore_all_permissions.sh

# 2. 清除浏览器缓存并重新登录

# 3. 检查用户权限配置
# 在数据库中查询用户权限设置
```

### 问题4：端口冲突

**症状**：服务启动时报端口被占用

**解决方案**：
```bash
# 1. 查看端口占用情况
lsof -i :8080  # 前端端口
lsof -i :9999  # 后端端口

# 2. 杀死占用端口的进程
kill -9 [进程ID]

# 3. 或者修改配置使用其他端口
# 修改 jshERP-web/vue.config.js 中的端口配置
# 修改 jshERP-boot/src/main/resources/application.yml 中的端口配置
```

## 📋 部署检查清单

### 环境检查
- [ ] JDK 1.8+ 已安装并配置 JAVA_HOME
- [ ] MySQL 5.7+ 服务正常运行
- [ ] Node.js 14+ 已安装
- [ ] Maven 3.6+ 已安装（如果需要编译）

### 数据库检查
- [ ] jsh_erp 数据库存在
- [ ] 盘点相关表结构完整
- [ ] 数据库用户权限正确
- [ ] 数据库连接配置正确

### 服务检查
- [ ] 后端服务启动成功（端口9999）
- [ ] 前端服务启动成功（端口8080）
- [ ] API接口响应正常
- [ ] 静态资源加载正常

### 功能检查
- [ ] 用户可以正常登录
- [ ] 左侧导航栏显示完整
- [ ] 盘点模块菜单可见
- [ ] 盘点列表页面正常加载
- [ ] 新增盘点功能正常
- [ ] 盘点流程可以完整执行

### 权限检查
- [ ] 管理员用户权限配置正确
- [ ] 功能按钮正常显示
- [ ] 页面权限控制正常
- [ ] API权限验证正常

## 🎯 性能优化建议

### 数据库优化
```sql
-- 为盘点表添加索引（如果不存在）
CREATE INDEX idx_inventory_check_tenant_status ON jsh_inventory_check(tenant_id, status);
CREATE INDEX idx_inventory_check_detail_check_id ON jsh_inventory_check_detail(check_id);
CREATE INDEX idx_inventory_check_create_time ON jsh_inventory_check(create_time);
```

### 前端优化
```bash
# 1. 启用 gzip 压缩
# 在 nginx 配置中启用 gzip

# 2. 使用 CDN 加速
# 将静态资源部署到 CDN

# 3. 启用浏览器缓存
# 配置适当的缓存策略
```

### 后端优化
```yaml
# 在 application.yml 中配置连接池
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. **查看日志**：
   - 后端日志：`tail -f jshERP-boot/logs/application.log`
   - 前端日志：浏览器开发者工具 Console

2. **检查配置**：
   - 数据库连接配置
   - 端口配置
   - 权限配置

3. **重启服务**：
   - 重启后端服务
   - 重启前端服务
   - 清除浏览器缓存

4. **联系支持**：
   - 提供详细的错误信息
   - 提供系统环境信息
   - 提供操作步骤

---

**🎉 恭喜！您已成功部署 jshERP 盘点业务模块！**

现在您可以开始使用这个现代化的库存盘点系统来管理您的库存了。
