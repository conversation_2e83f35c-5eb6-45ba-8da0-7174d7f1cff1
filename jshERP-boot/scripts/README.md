# jshERP 权限恢复脚本使用说明

## 概述

当 jshERP 系统出现以下问题时，可以使用这些脚本进行一键恢复：
- 左侧导航栏消失或空白
- 功能按钮不显示（新增、修改、删除等按钮消失）
- 用户权限配置丢失

## 脚本文件说明

### 1. `restore_all_permissions.sh` - 一键恢复脚本（推荐）
**功能**: 自动执行完整的权限恢复流程
**适用场景**: 导航栏和功能按钮都有问题时使用

### 2. `restore_button_permissions.sql` - 数据库权限恢复
**功能**: 修复数据库中的权限配置
**适用场景**: 只有数据库权限问题时使用

### 3. `fix_frontend_permissions.js` - 前端权限修复
**功能**: 修复前端权限存储和读取机制
**适用场景**: 只有前端权限问题时使用

## 使用方法

### 方法一：一键恢复（推荐）

```bash
# 1. 进入项目根目录
cd /path/to/jshERP

# 2. 给脚本执行权限
chmod +x jshERP-boot/scripts/restore_all_permissions.sh

# 3. 执行一键恢复脚本
./jshERP-boot/scripts/restore_all_permissions.sh
```

### 方法二：分步执行

#### 步骤1：修复数据库权限
```bash
# 执行 SQL 脚本
mysql -u用户名 -p密码 数据库名 < jshERP-boot/scripts/restore_button_permissions.sql
```

#### 步骤2：修复前端权限
```bash
# 执行 Node.js 脚本
node jshERP-boot/scripts/fix_frontend_permissions.js
```

#### 步骤3：重启前端服务
```bash
cd jshERP-web
npm run serve
```

## 执行前准备

### 1. 环境要求
- MySQL 客户端已安装
- Node.js 已安装
- 在 jshERP 项目根目录下执行

### 2. 数据库信息准备
- 数据库主机地址（默认：localhost）
- 数据库端口（默认：3306）
- 数据库名称（默认：jsh_erp）
- 数据库用户名（默认：jsh_user）
- 数据库密码

### 3. 备份建议
执行前建议备份数据库：
```bash
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 执行后验证

### 1. 清除浏览器缓存
- 按 `Ctrl+Shift+Delete`
- 选择"全部时间"
- 勾选"缓存的图片和文件"
- 点击"清除数据"

### 2. 重新登录系统
- 访问 http://localhost:8080
- 使用管理员账户登录
- 用户名：管理员
- 密码：您设置的密码

### 3. 检查功能
- ✅ 左侧导航栏正常显示
- ✅ 各模块菜单可以正常点击
- ✅ 页面功能按钮正常显示（新增、修改、删除、查看、导出）

## 故障排查

### 问题1：脚本执行失败
**解决方案**：
1. 检查是否在项目根目录下执行
2. 检查数据库连接信息是否正确
3. 检查 MySQL 和 Node.js 是否已安装

### 问题2：数据库连接失败
**解决方案**：
1. 确认数据库服务是否启动
2. 检查数据库连接信息
3. 确认用户权限是否足够

### 问题3：前端服务启动失败
**解决方案**：
1. 检查 Node.js 版本是否兼容
2. 执行 `npm install` 重新安装依赖
3. 检查端口 8080 是否被占用

### 问题4：权限仍然不正常
**解决方案**：
1. 检查用户ID是否为63（脚本默认配置）
2. 检查租户ID是否为63
3. 手动执行SQL验证权限配置

## 自定义配置

### 修改用户ID和租户ID
如果您的管理员用户ID不是63，需要修改脚本中的相关配置：

**修改 SQL 脚本**：
```sql
-- 将63改为您的用户ID
INSERT IGNORE INTO jsh_user_business (type, key_id, value, btn_str, tenant_id, delete_flag) VALUES
('UserRole', '您的用户ID', '[10]', 'None', 您的租户ID, '0');
```

### 添加自定义权限
如果您有自定义的功能模块，需要在按钮权限配置中添加相应的功能ID：
```json
{"funId":您的功能ID,"btnStr":"1,2,3,4,7"}
```

## 技术支持

如果遇到无法解决的问题，请提供以下信息：
1. 错误日志信息
2. 数据库版本和配置
3. Node.js 版本
4. 浏览器控制台错误信息
5. 系统环境信息

## 版本历史

- **v1.0** (2024-12-23)
  - 初始版本
  - 支持导航栏和功能按钮权限恢复
  - 包含完整的验证和错误处理机制
