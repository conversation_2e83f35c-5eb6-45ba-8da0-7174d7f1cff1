# jshERP 权限问题快速修复指南

## 🚨 遇到问题时的快速解决方案

### 问题症状
- ❌ 登录后左侧导航栏空白或消失
- ❌ 页面功能按钮不显示（新增、修改、删除等）
- ❌ 点击菜单无反应或报权限错误

### 🚀 一键修复（推荐）

```bash
# 1. 进入项目根目录
cd /path/to/jshERP

# 2. 执行一键恢复脚本
./jshERP-boot/scripts/restore_all_permissions.sh
```

**执行过程中会提示输入数据库信息，按提示操作即可。**

### 📋 手动修复步骤

如果一键脚本无法使用，可以按以下步骤手动修复：

#### 步骤1：修复数据库权限
```sql
-- 连接数据库
mysql -u jsh_user -p jsh_erp

-- 执行修复SQL
source jshERP-boot/scripts/restore_button_permissions.sql;
```

#### 步骤2：修复前端权限
```bash
# 执行前端修复脚本
node jshERP-boot/scripts/fix_frontend_permissions.js
```

#### 步骤3：重启服务
```bash
# 重启前端服务
cd jshERP-web
npm run serve
```

#### 步骤4：清除缓存并重新登录
1. 清除浏览器缓存 (Ctrl+Shift+Delete)
2. 访问 http://localhost:8080
3. 使用"管理员"账户重新登录

## 🔧 常见问题解决

### Q1: 脚本提示"权限不足"
```bash
# 给脚本添加执行权限
chmod +x jshERP-boot/scripts/restore_all_permissions.sh
```

### Q2: 数据库连接失败
检查数据库配置：
- 主机：localhost
- 端口：3306
- 数据库：jsh_erp
- 用户：jsh_user
- 密码：您设置的密码

### Q3: 前端服务启动失败
```bash
# 重新安装依赖
cd jshERP-web
npm install
npm run serve
```

### Q4: 修复后仍有问题
1. 强制刷新浏览器 (Ctrl+F5)
2. 检查浏览器控制台错误
3. 重新执行修复脚本

## 📞 紧急联系

如果以上方法都无法解决问题，请：
1. 备份当前数据库
2. 记录错误信息
3. 联系技术支持

## ⚡ 预防措施

为避免再次出现权限问题：
1. 定期备份数据库
2. 删除模块前先备份权限配置
3. 测试环境先验证再部署到生产环境

---

**💡 提示：建议将此文档保存到桌面，以便问题发生时快速查阅。**
