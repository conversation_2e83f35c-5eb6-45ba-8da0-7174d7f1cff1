-- =====================================================
-- jshERP 按钮权限一键恢复脚本
-- 版本: 1.0
-- 创建日期: 2024-12-23
-- 说明: 用于快速恢复系统功能按钮权限配置
-- =====================================================

-- 使用说明：
-- 1. 备份数据库：mysqldump -u用户名 -p密码 数据库名 > backup.sql
-- 2. 执行此脚本：mysql -u用户名 -p密码 数据库名 < restore_button_permissions.sql
-- 3. 重启前端服务并清除浏览器缓存
-- 4. 重新登录系统验证功能

-- =====================================================
-- 第一步：修复数据库结构和权限配置
-- =====================================================

-- 1.1 扩展btn_str字段长度以支持更多权限配置
ALTER TABLE jsh_user_business MODIFY COLUMN btn_str TEXT;

-- 1.2 修复权限配置的type字段名称不匹配问题
UPDATE jsh_user_business 
SET type = 'RoleFunctions' 
WHERE type = 'RoleFunction';

-- 1.3 启用所有功能菜单
UPDATE jsh_function 
SET state = 1, enabled = 1 
WHERE delete_flag = '0';

-- =====================================================
-- 第二步：清理可能存在的错误权限配置
-- =====================================================

-- 2.1 删除可能存在的重复或错误权限配置
DELETE FROM jsh_user_business 
WHERE type = 'RoleFunctions' 
AND (key_id LIKE '%_system' OR key_id LIKE '%_temp');

-- 2.2 确保用户角色关系正确
-- 为管理员用户(ID:63)分配租户角色(ID:10)
INSERT IGNORE INTO jsh_user_business (type, key_id, value, btn_str, tenant_id, delete_flag) VALUES
('UserRole', '63', '[10]', 'None', 63, '0');

-- =====================================================
-- 第三步：配置完整的功能权限
-- =====================================================

-- 3.1 为角色10配置完整的功能权限
INSERT INTO jsh_user_business (type, key_id, value, btn_str, tenant_id, delete_flag) VALUES
('RoleFunctions', '10', '[1][13][14][15][16][236][245][243][18][234][258][21][22][23][220][247][24][25][217][218][26][194][195][31][198][59][207][208][209][259][226][227][228][229][235][237][244][246][248][225][210][211][32][33][241][261][199][281][283][38][242][41][200][295][296][297][298][299][44][197][203][204][205][206][212][239][201][202][40][232][233][273][270][274][275][276][301][290][291][292][293][307][302][303][304][305]', '', 63, '0')
ON DUPLICATE KEY UPDATE 
value = '[1][13][14][15][16][236][245][243][18][234][258][21][22][23][220][247][24][25][217][218][26][194][195][31][198][59][207][208][209][259][226][227][228][229][235][237][244][246][248][225][210][211][32][33][241][261][199][281][283][38][242][41][200][295][296][297][298][299][44][197][203][204][205][206][212][239][201][202][40][232][233][273][270][274][275][276][301][290][291][292][293][307][302][303][304][305]';

-- =====================================================
-- 第四步：配置完整的按钮权限
-- =====================================================

-- 4.1 为角色10配置完整的按钮权限（所有79个功能模块）
UPDATE jsh_user_business 
SET btn_str = '[{"funId":1,"btnStr":"1,2,3,4,7"},{"funId":13,"btnStr":"1,2,3,4,7"},{"funId":14,"btnStr":"1,2,3,4,7"},{"funId":15,"btnStr":"1,2,3,4,7"},{"funId":16,"btnStr":"1,2,3,4,7"},{"funId":236,"btnStr":"1,2,3,4,7"},{"funId":245,"btnStr":"1,2,3,4,7"},{"funId":243,"btnStr":"1,2,3,4,7"},{"funId":18,"btnStr":"1,2,3,4,7"},{"funId":234,"btnStr":"1,2,3,4,7"},{"funId":258,"btnStr":"1,2,3,4,7"},{"funId":21,"btnStr":"1,2,3,4,7"},{"funId":22,"btnStr":"1,2,3,4,7"},{"funId":23,"btnStr":"1,2,3,4,7"},{"funId":220,"btnStr":"1,2,3,4,7"},{"funId":247,"btnStr":"1,2,3,4,7"},{"funId":24,"btnStr":"1,2,3,4,7"},{"funId":25,"btnStr":"1,2,3,4,7"},{"funId":217,"btnStr":"1,2,3,4,7"},{"funId":218,"btnStr":"1,2,3,4,7"},{"funId":26,"btnStr":"1,2,3,4,7"},{"funId":194,"btnStr":"1,2,3,4,7"},{"funId":195,"btnStr":"1,2,3,4,7"},{"funId":31,"btnStr":"1,2,3,4,7"},{"funId":198,"btnStr":"1,2,3,4,7"},{"funId":59,"btnStr":"1,2,3,4,7"},{"funId":207,"btnStr":"1,2,3,4,7"},{"funId":208,"btnStr":"1,2,3,4,7"},{"funId":209,"btnStr":"1,2,3,4,7"},{"funId":259,"btnStr":"1,2,3,4,7"},{"funId":226,"btnStr":"1,2,3,4,7"},{"funId":227,"btnStr":"1,2,3,4,7"},{"funId":228,"btnStr":"1,2,3,4,7"},{"funId":229,"btnStr":"1,2,3,4,7"},{"funId":235,"btnStr":"1,2,3,4,7"},{"funId":237,"btnStr":"1,2,3,4,7"},{"funId":244,"btnStr":"1,2,3,4,7"},{"funId":246,"btnStr":"1,2,3,4,7"},{"funId":248,"btnStr":"1,2,3,4,7"},{"funId":225,"btnStr":"1,2,3,4,7"},{"funId":210,"btnStr":"1,2,3,4,7"},{"funId":211,"btnStr":"1,2,3,4,7"},{"funId":32,"btnStr":"1,2,3,4,7"},{"funId":33,"btnStr":"1,2,3,4,7"},{"funId":241,"btnStr":"1,2,3,7"},{"funId":261,"btnStr":"1,2,3,4,7"},{"funId":199,"btnStr":"1,2,3,4,7"},{"funId":281,"btnStr":"1,2,3,4,7"},{"funId":283,"btnStr":"1,2,3,4,7"},{"funId":38,"btnStr":"1,2,3,4,7"},{"funId":242,"btnStr":"1,2,3,4,7"},{"funId":41,"btnStr":"1,2,3,4,7"},{"funId":200,"btnStr":"1,2,3,4,7"},{"funId":295,"btnStr":"1,2,3,4,7"},{"funId":296,"btnStr":"1,2,3,4,7"},{"funId":297,"btnStr":"1,2,3,4,7"},{"funId":298,"btnStr":"1,2,3,4,7"},{"funId":299,"btnStr":"1,2,3,4,7"},{"funId":44,"btnStr":"1,2,3,4,7"},{"funId":197,"btnStr":"1,2,3,4,7"},{"funId":203,"btnStr":"1,2,3,4,7"},{"funId":204,"btnStr":"1,2,3,4,7"},{"funId":205,"btnStr":"1,2,3,4,7"},{"funId":206,"btnStr":"1,2,3,4,7"},{"funId":212,"btnStr":"1,2,3,4,7"},{"funId":239,"btnStr":"1,2,3,4,7"},{"funId":201,"btnStr":"1,2,3,4,7"},{"funId":202,"btnStr":"1,2,3,4,7"},{"funId":40,"btnStr":"1,2,3,4,7"},{"funId":232,"btnStr":"1,2,3,4,7"},{"funId":233,"btnStr":"1,2,3,4,7"},{"funId":273,"btnStr":"1,2,3,4,7"},{"funId":270,"btnStr":"1,2,3,4,7"},{"funId":274,"btnStr":"1,2,3,4,7"},{"funId":275,"btnStr":"1,2,3,4,7"},{"funId":276,"btnStr":"1,2,3,4,7"},{"funId":301,"btnStr":"1,2,3,4,7"},{"funId":290,"btnStr":"1,2,3,4,7"},{"funId":291,"btnStr":"1,2,3,4,7"},{"funId":292,"btnStr":"1,2,3,4,7"},{"funId":293,"btnStr":"1,2,3,4,7"},{"funId":307,"btnStr":"1,2,3,4,7"},{"funId":302,"btnStr":"1,2,3,4,7"},{"funId":303,"btnStr":"1,2,3,4,7"},{"funId":304,"btnStr":"1,2,3,4,7"},{"funId":305,"btnStr":"1,2,3,4,7"}]'
WHERE type = 'RoleFunctions' AND key_id = '10';

-- =====================================================
-- 第五步：验证权限配置
-- =====================================================

-- 5.1 验证用户角色关系
SELECT '用户角色关系验证:' as '检查项目';
SELECT u.id, u.username, ub.value as '分配角色' 
FROM jsh_user u 
LEFT JOIN jsh_user_business ub ON u.id = ub.key_id AND ub.type = 'UserRole'
WHERE u.id = 63;

-- 5.2 验证角色功能权限
SELECT '角色功能权限验证:' as '检查项目';
SELECT type, key_id, 
       CASE WHEN LENGTH(value) > 50 THEN CONCAT(LEFT(value, 50), '...') ELSE value END as '功能权限',
       CASE WHEN LENGTH(btn_str) > 50 THEN CONCAT(LEFT(btn_str, 50), '...') ELSE btn_str END as '按钮权限'
FROM jsh_user_business 
WHERE type = 'RoleFunctions' AND key_id = '10';

-- 5.3 验证功能菜单状态
SELECT '功能菜单状态验证:' as '检查项目';
SELECT COUNT(*) as '启用菜单数量' FROM jsh_function WHERE delete_flag = '0' AND state = 1;
SELECT COUNT(*) as '禁用菜单数量' FROM jsh_function WHERE delete_flag = '0' AND state = 0;

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT '==================================================' as '执行结果';
SELECT '按钮权限恢复脚本执行完成！' as '状态';
SELECT '请按以下步骤完成恢复：' as '后续操作';
SELECT '1. 重启前端服务 (npm run serve)' as '步骤1';
SELECT '2. 清除浏览器缓存 (Ctrl+Shift+Delete)' as '步骤2';
SELECT '3. 重新登录系统验证功能' as '步骤3';
SELECT '4. 检查各模块的功能按钮是否正常显示' as '步骤4';
SELECT '==================================================' as '完成';

-- 注意事项：
-- 1. 此脚本适用于租户ID为63的管理员用户
-- 2. 如果用户ID或租户ID不同，请修改相应的数值
-- 3. 执行前请务必备份数据库
-- 4. 如有自定义权限配置，请在执行前保存相关数据
