#!/usr/bin/env node

/**
 * jshERP 前端权限修复脚本
 * 版本: 1.0
 * 创建日期: 2024-12-23
 * 说明: 自动修复前端权限存储和读取不一致的问题
 */

const fs = require('fs');
const path = require('path');

// 配置
const config = {
  projectRoot: process.cwd(),
  frontendPath: 'jshERP-web',
  backupSuffix: '.backup',
  encoding: 'utf8'
};

// 日志函数
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '[INFO]',
    warn: '[WARN]',
    error: '[ERROR]',
    success: '[SUCCESS]'
  }[type] || '[INFO]';
  
  console.log(`${timestamp} ${prefix} ${message}`);
}

// 检查文件是否存在
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// 备份文件
function backupFile(filePath) {
  try {
    const backupPath = filePath + config.backupSuffix;
    fs.copyFileSync(filePath, backupPath);
    log(`已备份文件: ${filePath} -> ${backupPath}`);
    return true;
  } catch (error) {
    log(`备份文件失败: ${filePath} - ${error.message}`, 'error');
    return false;
  }
}

// 修复 permission.js 文件
function fixPermissionJs() {
  const permissionPath = path.join(config.projectRoot, config.frontendPath, 'src', 'permission.js');
  
  if (!fileExists(permissionPath)) {
    log(`文件不存在: ${permissionPath}`, 'error');
    return false;
  }

  // 备份原文件
  if (!backupFile(permissionPath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(permissionPath, config.encoding);
    
    // 检查是否已经修复过
    if (content.includes('sessionStorage.setItem(USER_AUTH')) {
      log('permission.js 已经修复过，跳过修复');
      return true;
    }

    // 修复导入语句
    const oldImport = "import { USER_ID,INDEX_MAIN_PAGE_PATH } from '@/store/mutation-types'";
    const newImport = "import { USER_ID,INDEX_MAIN_PAGE_PATH,USER_AUTH } from '@/store/mutation-types'";
    
    if (content.includes(oldImport)) {
      content = content.replace(oldImport, newImport);
      log('已修复 mutation-types 导入语句');
    }

    // 修复按钮权限缓存逻辑
    const oldCacheLogic = `          // 缓存用户的按钮权限
          store.dispatch('GetUserBtnList').then(res => {
            Vue.ls.set('winBtnStrList', res.data.userBtn, 7 * 24 * 60 * 60 * 1000)
          })`;

    const newCacheLogic = `          // 缓存用户的按钮权限
          store.dispatch('GetUserBtnList').then(res => {
            Vue.ls.set('winBtnStrList', res.data.userBtn, 7 * 24 * 60 * 60 * 1000)
            // 同时设置到sessionStorage中供按钮权限验证使用
            if(res.data.userBtn) {
              sessionStorage.setItem(USER_AUTH, JSON.stringify(res.data.userBtn))
            }
          })`;

    if (content.includes(oldCacheLogic)) {
      content = content.replace(oldCacheLogic, newCacheLogic);
      log('已修复按钮权限缓存逻辑');
    } else {
      // 尝试更灵活的匹配
      const pattern = /store\.dispatch\('GetUserBtnList'\)\.then\(res => \{\s*Vue\.ls\.set\('winBtnStrList', res\.data\.userBtn, 7 \* 24 \* 60 \* 60 \* 1000\)\s*\}\)/;
      const replacement = `store.dispatch('GetUserBtnList').then(res => {
            Vue.ls.set('winBtnStrList', res.data.userBtn, 7 * 24 * 60 * 60 * 1000)
            // 同时设置到sessionStorage中供按钮权限验证使用
            if(res.data.userBtn) {
              sessionStorage.setItem(USER_AUTH, JSON.stringify(res.data.userBtn))
            }
          })`;
      
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        log('已修复按钮权限缓存逻辑（灵活匹配）');
      } else {
        log('未找到需要修复的按钮权限缓存逻辑', 'warn');
      }
    }

    // 写入修复后的内容
    fs.writeFileSync(permissionPath, content, config.encoding);
    log('permission.js 修复完成', 'success');
    return true;

  } catch (error) {
    log(`修复 permission.js 失败: ${error.message}`, 'error');
    return false;
  }
}

// 验证修复结果
function verifyFix() {
  const permissionPath = path.join(config.projectRoot, config.frontendPath, 'src', 'permission.js');
  
  if (!fileExists(permissionPath)) {
    log('验证失败: permission.js 文件不存在', 'error');
    return false;
  }

  try {
    const content = fs.readFileSync(permissionPath, config.encoding);
    
    const checks = [
      {
        name: 'USER_AUTH 导入',
        test: content.includes('USER_AUTH') && content.includes("from '@/store/mutation-types'")
      },
      {
        name: 'sessionStorage 设置',
        test: content.includes('sessionStorage.setItem(USER_AUTH')
      },
      {
        name: 'JSON.stringify 处理',
        test: content.includes('JSON.stringify(res.data.userBtn)')
      }
    ];

    let allPassed = true;
    checks.forEach(check => {
      if (check.test) {
        log(`✓ ${check.name} - 通过`);
      } else {
        log(`✗ ${check.name} - 失败`, 'error');
        allPassed = false;
      }
    });

    return allPassed;

  } catch (error) {
    log(`验证修复结果失败: ${error.message}`, 'error');
    return false;
  }
}

// 清理备份文件
function cleanupBackups() {
  const permissionPath = path.join(config.projectRoot, config.frontendPath, 'src', 'permission.js');
  const backupPath = permissionPath + config.backupSuffix;
  
  if (fileExists(backupPath)) {
    try {
      fs.unlinkSync(backupPath);
      log('已清理备份文件');
    } catch (error) {
      log(`清理备份文件失败: ${error.message}`, 'warn');
    }
  }
}

// 主函数
function main() {
  log('开始执行 jshERP 前端权限修复脚本');
  log(`项目根目录: ${config.projectRoot}`);
  log(`前端目录: ${path.join(config.projectRoot, config.frontendPath)}`);

  // 检查前端目录是否存在
  const frontendDir = path.join(config.projectRoot, config.frontendPath);
  if (!fileExists(frontendDir)) {
    log(`前端目录不存在: ${frontendDir}`, 'error');
    log('请确保在 jshERP 项目根目录下执行此脚本', 'error');
    process.exit(1);
  }

  // 修复 permission.js
  if (!fixPermissionJs()) {
    log('前端权限修复失败', 'error');
    process.exit(1);
  }

  // 验证修复结果
  if (!verifyFix()) {
    log('修复结果验证失败', 'error');
    process.exit(1);
  }

  log('前端权限修复完成！', 'success');
  log('');
  log('后续操作：');
  log('1. 重启前端服务: cd jshERP-web && npm run serve');
  log('2. 清除浏览器缓存: Ctrl+Shift+Delete');
  log('3. 重新登录系统验证功能');
  log('4. 检查各模块的功能按钮是否正常显示');
  
  // 询问是否清理备份文件
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('是否清理备份文件？(y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      cleanupBackups();
    }
    rl.close();
  });
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  fixPermissionJs,
  verifyFix,
  cleanupBackups
};
