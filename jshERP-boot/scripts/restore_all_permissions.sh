#!/bin/bash

# =====================================================
# jshERP 权限一键恢复脚本
# 版本: 1.0
# 创建日期: 2024-12-23
# 说明: 一键恢复导航栏和功能按钮权限
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖环境..."
    
    # 检查 MySQL 客户端
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL 客户端未安装，请先安装 MySQL"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查项目目录
    if [ ! -d "jshERP-web" ] || [ ! -d "jshERP-boot" ]; then
        log_error "请在 jshERP 项目根目录下执行此脚本"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 获取数据库配置
get_db_config() {
    log_info "获取数据库配置..."
    
    # 默认配置
    DB_HOST="localhost"
    DB_PORT="3306"
    DB_NAME="jsh_erp"
    DB_USER="jsh_user"
    
    # 从配置文件读取（如果存在）
    if [ -f "jshERP-boot/src/main/resources/application.yml" ]; then
        log_info "从配置文件读取数据库信息..."
        # 这里可以添加解析 YAML 配置的逻辑
    fi
    
    # 交互式输入
    echo -e "${YELLOW}请输入数据库配置信息（直接回车使用默认值）:${NC}"
    read -p "数据库主机 [$DB_HOST]: " input_host
    read -p "数据库端口 [$DB_PORT]: " input_port
    read -p "数据库名称 [$DB_NAME]: " input_name
    read -p "数据库用户 [$DB_USER]: " input_user
    read -s -p "数据库密码: " DB_PASSWORD
    echo
    
    # 使用输入值或默认值
    DB_HOST=${input_host:-$DB_HOST}
    DB_PORT=${input_port:-$DB_PORT}
    DB_NAME=${input_name:-$DB_NAME}
    DB_USER=${input_user:-$DB_USER}
    
    log_success "数据库配置获取完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="./backups/$backup_file"
    
    # 创建备份目录
    mkdir -p ./backups
    
    # 执行备份
    if mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_path" 2>/dev/null; then
        log_success "数据库备份完成: $backup_path"
        return 0
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 测试数据库连接
test_db_connection() {
    log_info "测试数据库连接..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1;" &>/dev/null; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败，请检查配置"
        return 1
    fi
}

# 执行数据库修复
restore_database_permissions() {
    log_info "执行数据库权限修复..."
    
    local sql_script="./jshERP-boot/scripts/restore_button_permissions.sql"
    
    if [ ! -f "$sql_script" ]; then
        log_error "SQL 修复脚本不存在: $sql_script"
        return 1
    fi
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$sql_script" 2>/dev/null; then
        log_success "数据库权限修复完成"
        return 0
    else
        log_error "数据库权限修复失败"
        return 1
    fi
}

# 修复前端权限
restore_frontend_permissions() {
    log_info "执行前端权限修复..."
    
    local js_script="./jshERP-boot/scripts/fix_frontend_permissions.js"
    
    if [ ! -f "$js_script" ]; then
        log_error "前端修复脚本不存在: $js_script"
        return 1
    fi
    
    if node "$js_script" 2>/dev/null; then
        log_success "前端权限修复完成"
        return 0
    else
        log_error "前端权限修复失败"
        return 1
    fi
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    # 检查前端服务是否在运行
    if pgrep -f "npm.*serve" > /dev/null; then
        log_info "停止前端服务..."
        pkill -f "npm.*serve"
        sleep 2
    fi
    
    # 启动前端服务
    log_info "启动前端服务..."
    cd jshERP-web
    nohup npm run serve > ../logs/frontend.log 2>&1 &
    cd ..
    
    sleep 5
    
    if pgrep -f "npm.*serve" > /dev/null; then
        log_success "前端服务启动成功"
    else
        log_warn "前端服务启动可能失败，请手动检查"
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    # 验证数据库权限配置
    local db_check=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT COUNT(*) FROM jsh_user_business WHERE type='RoleFunctions' AND key_id='10';" 2>/dev/null | tail -n 1)
    
    if [ "$db_check" -eq "1" ]; then
        log_success "数据库权限配置验证通过"
    else
        log_error "数据库权限配置验证失败"
        return 1
    fi
    
    # 验证前端文件修复
    if grep -q "sessionStorage.setItem(USER_AUTH" "./jshERP-web/src/permission.js"; then
        log_success "前端权限文件验证通过"
    else
        log_error "前端权限文件验证失败"
        return 1
    fi
    
    return 0
}

# 显示后续操作指南
show_next_steps() {
    echo
    echo -e "${GREEN}==================================================${NC}"
    echo -e "${GREEN}权限恢复完成！${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo
    echo -e "${YELLOW}后续操作：${NC}"
    echo "1. 清除浏览器缓存 (Ctrl+Shift+Delete)"
    echo "2. 访问系统: http://localhost:8080"
    echo "3. 使用管理员账户重新登录"
    echo "4. 检查左侧导航栏是否正常显示"
    echo "5. 检查各模块的功能按钮是否正常显示"
    echo
    echo -e "${YELLOW}如果仍有问题：${NC}"
    echo "1. 检查前端服务日志: tail -f logs/frontend.log"
    echo "2. 检查浏览器控制台错误信息"
    echo "3. 联系技术支持"
    echo
    echo -e "${GREEN}==================================================${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}==================================================${NC}"
    echo -e "${BLUE}jshERP 权限一键恢复脚本${NC}"
    echo -e "${BLUE}版本: 1.0${NC}"
    echo -e "${BLUE}==================================================${NC}"
    echo
    
    # 创建日志目录
    mkdir -p logs
    
    # 检查依赖
    check_dependencies
    
    # 获取数据库配置
    get_db_config
    
    # 测试数据库连接
    if ! test_db_connection; then
        exit 1
    fi
    
    # 询问是否备份
    echo
    read -p "是否备份数据库？(推荐) [Y/n]: " backup_choice
    if [[ $backup_choice != "n" && $backup_choice != "N" ]]; then
        if ! backup_database; then
            log_warn "备份失败，但继续执行修复"
        fi
    fi
    
    # 执行修复
    echo
    log_info "开始执行权限修复..."
    
    if ! restore_database_permissions; then
        log_error "数据库权限修复失败，停止执行"
        exit 1
    fi
    
    if ! restore_frontend_permissions; then
        log_error "前端权限修复失败，停止执行"
        exit 1
    fi
    
    # 重启服务
    restart_services
    
    # 验证修复结果
    if verify_fix; then
        show_next_steps
    else
        log_error "修复验证失败，请手动检查"
        exit 1
    fi
}

# 执行主函数
main "$@"
