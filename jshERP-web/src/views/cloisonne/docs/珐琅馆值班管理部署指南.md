# 珐琅馆值班管理模块部署指南

## 部署概述

本指南详细说明如何将珐琅馆值班管理模块部署到生产环境，包括前端构建、后端配置、数据库初始化和权限设置等步骤。

## 环境要求

### 服务器环境
- **操作系统**: Linux (CentOS 7+/Ubuntu 18+) 或 Windows Server 2016+
- **Java版本**: JDK 1.8+
- **Node.js版本**: 14.x+
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接

## 前端部署

### 1. 代码准备
```bash
# 克隆或更新代码
cd /path/to/jshERP-web

# 安装依赖
npm install

# 检查代码完整性
ls -la src/views/cloisonne/
```

### 2. 环境配置
编辑 `.env.production` 文件：
```bash
# API基础URL
VUE_APP_API_BASE_URL=https://your-domain.com/api

# 应用标题
VUE_APP_TITLE=聆花文化ERP

# 是否启用调试
VUE_APP_DEBUG=false
```

### 3. 构建生产版本
```bash
# 构建生产版本
npm run build

# 检查构建结果
ls -la dist/
```

### 4. 部署到Web服务器

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # 前端静态文件
    location / {
        root /path/to/jshERP-web/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:9999/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /path/to/jshERP-web/dist
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/ssl/cert.pem
    SSLCertificateKeyFile /path/to/ssl/key.pem
    
    # 前端路由支持
    <Directory "/path/to/jshERP-web/dist">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # 单页应用路由
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API代理
    ProxyPreserveHost On
    ProxyPass /api/ http://localhost:9999/
    ProxyPassReverse /api/ http://localhost:9999/
</VirtualHost>
```

## 后端部署

### 1. 数据库初始化

#### 创建数据库表
```sql
-- 排班记录表
CREATE TABLE `jsh_cloisonne_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_id` bigint(20) NOT NULL COMMENT '员工ID',
  `employee_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `duty_date` date NOT NULL COMMENT '值班日期',
  `shift_type` varchar(20) NOT NULL COMMENT '班次类型：早班/晚班/全天',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `work_hours` decimal(4,1) DEFAULT NULL COMMENT '工作时长(小时)',
  `work_area` varchar(100) DEFAULT NULL COMMENT '工作区域',
  `status` varchar(20) DEFAULT 'normal' COMMENT '状态：normal/leave/swap',
  `priority` varchar(20) DEFAULT 'normal' COMMENT '优先级：normal/high',
  `notes` text COMMENT '备注信息',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_date` (`tenant_id`, `duty_date`),
  KEY `idx_employee` (`employee_id`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='珐琅馆排班记录表';

-- 班次定义表
CREATE TABLE `jsh_schedule_shift` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shift_name` varchar(50) NOT NULL COMMENT '班次名称',
  `shift_code` varchar(20) NOT NULL COMMENT '班次编码',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `work_hours` decimal(4,1) NOT NULL COMMENT '工作时长',
  `color_code` varchar(10) DEFAULT NULL COMMENT '颜色代码',
  `description` varchar(200) DEFAULT NULL COMMENT '班次描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_code` (`tenant_id`, `shift_code`, `delete_flag`),
  KEY `idx_tenant_active` (`tenant_id`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班次定义表';
```

#### 初始化基础数据
```sql
-- 插入默认班次
INSERT INTO `jsh_schedule_shift` (`shift_name`, `shift_code`, `start_time`, `end_time`, `work_hours`, `color_code`, `description`, `tenant_id`) VALUES
('早班', 'MORNING', '09:00:00', '13:00:00', 4.0, '#fa8c16', '上午时段值班', 63),
('晚班', 'EVENING', '14:00:00', '18:00:00', 4.0, '#52c41a', '下午时段值班', 63),
('全天', 'FULLDAY', '09:00:00', '18:00:00', 8.0, '#1890ff', '全天候值班', 63);

-- 插入示例排班数据（可选）
INSERT INTO `jsh_cloisonne_schedule` (`employee_id`, `employee_name`, `duty_date`, `shift_type`, `start_time`, `end_time`, `work_hours`, `work_area`, `status`, `priority`, `notes`, `tenant_id`) VALUES
(18, '聆花老师', '2025-06-23', '早班', '09:00:00', '13:00:00', 4.0, '展示区', 'normal', 'normal', '正常值班', 63),
(19, '梁朝伟', '2025-06-23', '晚班', '14:00:00', '18:00:00', 4.0, '制作区', 'normal', 'normal', '正常值班', 63),
(20, '龚锦华', '2025-06-24', '全天', '09:00:00', '18:00:00', 8.0, '全区域', 'normal', 'high', '全天值班', 63),
(21, '伍尚明', '2025-06-25', '早班', '09:00:00', '13:00:00', 4.0, '接待区', 'normal', 'normal', '接待客户', 63),
(22, '莫智华', '2025-06-26', '晚班', '14:00:00', '18:00:00', 4.0, '制作区', 'normal', 'normal', '制作指导', 63);
```

### 2. 应用配置

#### application.yml配置
```yaml
# 数据源配置
spring:
  datasource:
    url: *************************************************************************************************
    username: jsh_user
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
    timeout: 3000ms
    
# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: org.jeecg.modules.cloisonne.entity
  configuration:
    map-underscore-to-camel-case: true
    
# 日志配置
logging:
  level:
    org.jeecg.modules.cloisonne: DEBUG
  file:
    name: logs/cloisonne-schedule.log
```

### 3. 部署应用

#### 使用systemd管理服务
创建服务文件 `/etc/systemd/system/jsherp.service`：
```ini
[Unit]
Description=jshERP Application
After=network.target

[Service]
Type=forking
User=jsherp
Group=jsherp
WorkingDirectory=/opt/jsherp
ExecStart=/opt/jsherp/bin/start.sh
ExecStop=/opt/jsherp/bin/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动脚本 `/opt/jsherp/bin/start.sh`：
```bash
#!/bin/bash
cd /opt/jsherp
nohup java -jar -Xms512m -Xmx2g -Dspring.profiles.active=prod jsherp.jar > logs/app.log 2>&1 &
echo $! > jsherp.pid
```

停止脚本 `/opt/jsherp/bin/stop.sh`：
```bash
#!/bin/bash
cd /opt/jsherp
if [ -f jsherp.pid ]; then
    kill $(cat jsherp.pid)
    rm jsherp.pid
fi
```

#### 启动服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start jsherp

# 设置开机自启
sudo systemctl enable jsherp

# 查看服务状态
sudo systemctl status jsherp
```

## 权限配置

### 1. 菜单权限配置

#### 在jsh_function表中添加菜单
```sql
INSERT INTO `jsh_function` (`id`, `number`, `name`, `p_number`, `url`, `component`, `state`, `sort`, `enabled`, `type`, `push_btn`, `icon`) VALUES
(307, '307', '珐琅馆排班', '30', '/cloisonne/schedule', 'cloisonne/CloisonneScheduleList', 0, 7, 1, 0, '[{\"title\":\"新增\",\"value\":\"add\"},{\"title\":\"编辑\",\"value\":\"edit\"},{\"title\":\"删除\",\"value\":\"delete\"},{\"title\":\"导出\",\"value\":\"export\"},{\"title\":\"导入\",\"value\":\"import\"},{\"title\":\"批量操作\",\"value\":\"batch\"}]', 'calendar');
```

### 2. 角色权限分配

#### 为管理员角色分配权限
```sql
-- 查询管理员角色ID
SELECT id FROM jsh_role WHERE name = '管理员';

-- 为角色分配菜单权限（假设角色ID为1）
INSERT INTO `jsh_role_function` (`role_id`, `function_id`) VALUES (1, 307);
```

### 3. 用户权限配置

#### 为用户分配业务权限
```sql
-- 为用户分配珐琅馆排班权限
INSERT INTO `jsh_user_business` (`type`, `key_id`, `value`, `btn_str`, `tenant_id`) VALUES
('UserRole', 1, '[1]', '[{\"funId\":\"307\",\"btnStr\":\"add,edit,delete,export,import,batch\"}]', 63);
```

## 监控和维护

### 1. 日志监控

#### 应用日志
```bash
# 查看应用日志
tail -f /opt/jsherp/logs/app.log

# 查看错误日志
grep ERROR /opt/jsherp/logs/app.log

# 查看珐琅馆模块日志
grep "cloisonne" /opt/jsherp/logs/app.log
```

#### Nginx访问日志
```bash
# 查看访问日志
tail -f /var/log/nginx/access.log

# 分析访问统计
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr
```

### 2. 性能监控

#### 系统资源监控
```bash
# CPU和内存使用情况
top -p $(cat /opt/jsherp/jsherp.pid)

# 磁盘使用情况
df -h

# 网络连接状态
netstat -tlnp | grep :9999
```

#### 数据库监控
```sql
-- 查看数据库连接数
SHOW PROCESSLIST;

-- 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'jsh_erp' 
AND table_name LIKE 'jsh_cloisonne%';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

### 3. 备份策略

#### 数据库备份
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="jsh_erp"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u root -p$MYSQL_PASSWORD $DB_NAME > $BACKUP_DIR/jsh_erp_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/jsh_erp_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

#### 应用备份
```bash
#!/bin/bash
# 应用备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/app"
APP_DIR="/opt/jsherp"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/jsherp_$DATE.tar.gz -C $APP_DIR .

# 删除30天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 4. 故障排查

#### 常见问题及解决方案

**问题1：页面无法访问**
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查端口占用
netstat -tlnp | grep :80

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log
```

**问题2：API接口报错**
```bash
# 检查应用状态
sudo systemctl status jsherp

# 查看应用日志
tail -f /opt/jsherp/logs/app.log

# 检查数据库连接
mysql -u jsh_user -p -e "SELECT 1"
```

**问题3：数据库连接失败**
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 检查数据库配置
grep -A 10 "datasource" /opt/jsherp/application.yml

# 测试数据库连接
mysql -u jsh_user -p jsh_erp -e "SHOW TABLES"
```

## 安全配置

### 1. 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# 限制数据库端口访问
sudo ufw allow from 127.0.0.1 to any port 3306

# 启用防火墙
sudo ufw enable
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt获取免费SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER 'jsherp_app'@'localhost' IDENTIFIED BY 'strong_password';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON jsh_erp.* TO 'jsherp_app'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 版本升级

### 1. 升级前准备
```bash
# 备份数据库
mysqldump -u root -p jsh_erp > backup_before_upgrade.sql

# 备份应用文件
tar -czf app_backup_before_upgrade.tar.gz /opt/jsherp

# 记录当前版本
echo "$(date): 升级前版本信息" >> upgrade.log
```

### 2. 升级步骤
```bash
# 停止应用
sudo systemctl stop jsherp

# 更新代码
cd /path/to/source
git pull origin main

# 构建新版本
npm run build

# 部署新版本
cp -r dist/* /var/www/html/

# 更新后端应用
cp target/jsherp.jar /opt/jsherp/

# 启动应用
sudo systemctl start jsherp
```

### 3. 升级验证
```bash
# 检查应用状态
sudo systemctl status jsherp

# 验证功能
curl -I http://localhost/cloisonne/schedule

# 查看日志
tail -f /opt/jsherp/logs/app.log
```

---

**部署指南版本**: v1.0  
**最后更新**: 2025-06-23  
**适用环境**: 生产环境部署
