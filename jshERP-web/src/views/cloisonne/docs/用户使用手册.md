# 珐琅馆智能排班系统用户使用手册

## 目录
1. [系统概述](#系统概述)
2. [快速入门](#快速入门)
3. [功能详解](#功能详解)
4. [AI智能排班](#AI智能排班)
5. [高级功能](#高级功能)
6. [常见问题](#常见问题)
7. [最佳实践](#最佳实践)

## 系统概述

### 系统简介
珐琅馆智能排班系统是基于jshERP平台开发的企业级排班管理解决方案，集成了人工智能算法，能够自动生成最优的员工排班方案。

### 主要功能
- **手动排班管理**：支持手动创建、编辑、删除排班记录
- **AI智能排班**：基于遗传算法的自动排班优化
- **员工偏好管理**：配置员工的工作时间偏好和约束条件
- **排班模板**：创建和管理可重复使用的排班模板
- **数据统计分析**：提供详细的排班数据统计和分析报告

### 系统特色
- 🤖 **智能算法**：采用遗传算法进行多目标优化
- 📊 **可视化界面**：现代化的用户界面，操作简单直观
- 🔧 **灵活配置**：支持复杂的约束条件和优化目标设置
- 📱 **响应式设计**：完美适配桌面、平板、手机等设备
- 🔒 **权限控制**：完善的多租户权限管理体系

## 快速入门

### 第一步：访问系统
1. 登录jshERP系统
2. 在左侧菜单中找到"掐丝珐琅馆"
3. 点击"珐琅馆排班"进入排班管理页面

### 第二步：查看排班列表
- 系统默认显示当前月份的排班记录
- 可以通过日期筛选查看不同时间段的排班
- 支持按员工、班次类型等条件筛选

### 第三步：创建排班记录
1. 点击"新增"按钮
2. 选择员工、排班日期、班次类型
3. 设置开始时间、结束时间
4. 添加备注信息（可选）
5. 点击"确定"保存

### 第四步：使用AI智能排班
1. 切换到"AI智能排班"选项卡
2. 设置时间范围和参与员工
3. 配置约束条件和优化目标
4. 点击"开始生成"启动AI排班
5. 查看生成结果并应用方案

## 功能详解

### 手动排班管理

#### 排班列表
- **列表视图**：以表格形式显示排班记录
- **日历视图**：以日历形式直观显示排班安排
- **周月视图**：切换周视图和月视图查看模式

#### 排班操作
- **新增排班**：手动创建新的排班记录
- **编辑排班**：修改现有排班记录的信息
- **删除排班**：删除不需要的排班记录
- **批量操作**：支持批量删除、批量修改等操作

#### 数据筛选
- **日期筛选**：按日期范围筛选排班记录
- **员工筛选**：按员工姓名筛选排班记录
- **班次筛选**：按班次类型筛选排班记录
- **状态筛选**：按排班状态筛选记录

### 员工管理
- **员工信息**：查看和管理员工基本信息
- **技能标签**：为员工设置技能标签
- **工作状态**：管理员工的工作状态

## AI智能排班

### 基础配置

#### 时间设置
- **开始日期**：选择排班开始日期
- **结束日期**：选择排班结束日期
- **时间范围**：支持1-30天的排班周期

#### 员工选择
- **全选功能**：一键选择所有员工
- **多选模式**：支持选择多个员工参与排班
- **员工筛选**：按部门、技能等条件筛选员工

#### 算法选择
- **遗传算法**：适用于复杂约束的优化问题
- **模拟退火**：适用于快速收敛的场景
- **粒子群优化**：适用于多目标优化

### 约束条件配置

#### 员工约束
- **可用时间**：设置员工每天的可工作时间段
- **偏好班次**：配置员工偏好的班次类型
- **避免班次**：设置员工不愿意工作的班次
- **最大连续天数**：限制员工连续工作的最大天数
- **技能要求**：根据员工技能分配合适的班次

#### 时间约束
- **最少人数**：设置每个班次的最少人员配置
- **最多人数**：设置每个班次的最多人员配置
- **必须覆盖**：设置必须有人值班的时间段
- **班次间隔**：设置班次之间的最小间隔时间

#### 业务约束
- **特殊事件**：配置需要额外人员的特殊事件
- **节假日安排**：设置节假日的特殊排班要求
- **客流高峰**：根据客流量调整人员配置

### 优化目标设置

#### 四大优化目标
1. **覆盖率**（默认40%）
   - 确保所有时段都有足够的人员覆盖
   - 避免出现无人值班的情况

2. **公平性**（默认30%）
   - 平衡员工之间的工作量
   - 避免某些员工工作过多或过少

3. **满意度**（默认20%）
   - 尽量满足员工的时间偏好
   - 考虑员工的个人需求

4. **成本效率**（默认10%）
   - 在满足业务需求的前提下控制人力成本
   - 避免人员配置过多

#### 权重调整
- **滑块调整**：通过滑块直观调整各目标权重
- **数值输入**：精确输入权重数值
- **预设方案**：使用系统预设的权重方案
- **权重验证**：系统自动验证权重总和为100%

### 生成过程监控

#### 实时进度
- **进度条**：显示算法执行进度
- **当前阶段**：显示算法当前执行阶段
- **代数信息**：显示遗传算法的当前代数
- **适应度**：显示当前最优解的适应度

#### 状态信息
- **已用时间**：显示算法已运行时间
- **预计剩余**：估算剩余执行时间
- **收敛状态**：显示算法收敛情况

### 结果分析

#### 优化指标
- **覆盖率**：显示时段覆盖的完整程度
- **公平性**：显示工作量分配的均衡程度
- **满意度**：显示员工偏好的满足程度
- **成本效率**：显示人力成本的控制效果

#### 排班预览
- **甘特图**：以甘特图形式展示排班结果
- **拖拽编辑**：支持拖拽调整排班安排
- **实时统计**：显示排班的实时统计信息
- **冲突检测**：自动检测并标记排班冲突

#### 优化建议
- **智能分析**：基于AI分析提供优化建议
- **问题识别**：自动识别排班中的潜在问题
- **解决方案**：提供具体的改进建议
- **一键应用**：支持一键应用优化建议

## 高级功能

### 员工偏好管理

#### 工作时间偏好
- **每日时间**：设置员工每天的可工作时间
- **周工作模式**：配置员工的周工作模式
- **灵活时间**：支持弹性工作时间设置

#### 班次偏好
- **偏好班次**：设置员工喜欢的班次类型
- **避免班次**：设置员工不愿意的班次类型
- **班次优先级**：为不同班次设置优先级

#### 特殊要求
- **医疗需求**：考虑员工的医疗需求
- **家庭因素**：考虑员工的家庭情况
- **教育培训**：考虑员工的学习安排
- **其他要求**：其他个性化需求

### 排班模板管理

#### 模板分类
- **标准模板**：适用于日常排班的标准配置
- **节假日模板**：适用于节假日的特殊排班
- **自定义模板**：用户自定义的排班模板
- **行业模板**：针对不同行业的专业模板

#### 模板操作
- **创建模板**：基于当前配置创建新模板
- **编辑模板**：修改现有模板的配置
- **复制模板**：快速复制现有模板
- **应用模板**：将模板应用到当前排班

#### 模板共享
- **公共模板**：设置模板为公共可用
- **私有模板**：设置模板为个人专用
- **导入导出**：支持模板的导入导出功能

### 数据统计分析

#### 排班统计
- **工作量统计**：统计员工的工作量分布
- **班次统计**：统计各班次的人员配置
- **时间统计**：统计不同时间段的排班情况

#### 趋势分析
- **工作量趋势**：分析员工工作量的变化趋势
- **满意度趋势**：分析员工满意度的变化
- **效率趋势**：分析排班效率的变化

#### 报表导出
- **Excel导出**：导出详细的排班数据
- **PDF报告**：生成专业的排班报告
- **图表导出**：导出可视化图表

## 常见问题

### Q1: AI排班生成时间过长怎么办？
**A**: 可以尝试以下方法：
- 减少参与排班的员工数量
- 缩短排班时间范围
- 简化约束条件
- 降低算法精度要求

### Q2: 生成的排班方案不满意怎么办？
**A**: 可以进行以下调整：
- 调整优化目标的权重配置
- 修改约束条件设置
- 使用手动编辑功能微调
- 重新生成排班方案

### Q3: 如何处理员工的临时请假？
**A**: 系统提供以下解决方案：
- 在员工约束中设置不可用时间
- 使用手动编辑功能调整排班
- 重新运行AI排班生成新方案

### Q4: 如何确保重要时段有足够人员？
**A**: 可以通过以下方式：
- 在时间约束中设置最少人数要求
- 配置必须覆盖的时间段
- 提高覆盖率的优化权重
- 添加特殊事件配置

### Q5: 系统支持多少员工同时排班？
**A**: 系统理论上支持无限员工，但建议：
- 员工数量控制在50人以内
- 排班天数控制在30天以内
- 以确保最佳的性能表现

## 最佳实践

### 排班规划建议
1. **提前规划**：建议提前1-2周进行排班规划
2. **定期调整**：根据业务变化定期调整排班策略
3. **员工沟通**：与员工充分沟通，了解其偏好和需求
4. **数据分析**：定期分析排班数据，优化排班策略

### 约束设置技巧
1. **循序渐进**：从简单约束开始，逐步增加复杂度
2. **平衡考虑**：在业务需求和员工需求之间找到平衡
3. **灵活调整**：根据实际情况灵活调整约束条件
4. **测试验证**：设置约束后进行测试验证

### 优化目标配置
1. **业务优先**：根据业务特点调整优化目标权重
2. **季节调整**：根据不同季节调整权重配置
3. **持续优化**：根据使用效果持续优化权重设置
4. **经验积累**：积累不同场景下的最佳权重配置

### 系统维护建议
1. **定期备份**：定期备份排班数据和配置
2. **权限管理**：合理设置用户权限，确保数据安全
3. **培训学习**：定期组织用户培训，提高使用效率
4. **反馈收集**：收集用户反馈，持续改进系统功能

---

**技术支持**：如有问题请联系系统管理员  
**文档版本**：v1.0  
**最后更新**：2025-06-23
