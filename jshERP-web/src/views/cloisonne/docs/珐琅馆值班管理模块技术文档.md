# 珐琅馆值班管理模块技术文档

## 项目概述

珐琅馆值班管理模块是基于jshERP系统开发的现代化排班管理解决方案，采用Vue.js 2.7.16 + Ant Design Vue 1.5.2技术栈，提供直观易用的排班管理界面和智能数据分析功能。

## 技术架构

### 前端技术栈
- **核心框架**: Vue.js 2.7.16
- **UI组件库**: Ant Design Vue 1.5.2
- **路由管理**: Vue Router 3.0.1
- **状态管理**: Vuex 3.1.0
- **HTTP客户端**: Axios 0.18.0
- **样式处理**: Less + CSS预处理器

### 后端技术栈
- **核心框架**: Spring Boot 2.x
- **数据访问层**: MyBatis + MyBatis Plus
- **数据库**: MySQL 5.7.33
- **缓存**: Redis 6.2.1
- **认证**: JWT Token

## 模块结构

### 目录结构
```
src/views/cloisonne/
├── CloisonneScheduleList.vue          # 主页面组件
├── components/                        # 视图组件
│   ├── DutyWeekMonthView.vue         # 周月视图组件
│   ├── DutyListView.vue              # 列表视图组件
│   └── DutyStatisticsView.vue        # 统计视图组件
├── modules/                          # 弹窗组件
│   ├── DutyFormModal.vue             # 排班表单弹窗
│   └── BatchDutyModal.vue            # 批量操作弹窗
└── docs/                             # 技术文档
    └── 珐琅馆值班管理模块技术文档.md
```

### 组件功能说明

#### 1. CloisonneScheduleList.vue (主页面)
- **功能**: 排班管理主页面，包含视图切换和公共操作
- **特色**: 
  - 三种视图模式切换（周月视图、列表视图、统计视图）
  - 月份导航控制
  - 统一的数据管理和事件处理

#### 2. DutyWeekMonthView.vue (周月视图)
- **功能**: 左侧周历 + 右侧月历的双栏布局
- **特色**:
  - 双击添加排班功能
  - 实时员工工时统计
  - 现代化卡片式排班显示
  - 智能悬停提示和操作反馈

#### 3. DutyListView.vue (列表视图)
- **功能**: 卡片网格式排班列表展示
- **特色**:
  - 多维度搜索筛选（员工、班次、状态）
  - 响应式卡片网格布局
  - 彩色头像和班次徽章
  - 分页和批量操作支持

#### 4. DutyStatisticsView.vue (统计视图)
- **功能**: 数据分析仪表板
- **特色**:
  - 统计概览卡片（总天数、员工数、平均值等）
  - 员工排行榜（金银铜排名设计）
  - 班次分布分析
  - 智能数据洞察和优化建议

## 数据库设计

### 主要数据表

#### jsh_cloisonne_schedule (排班记录表)
```sql
CREATE TABLE `jsh_cloisonne_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_id` bigint(20) NOT NULL COMMENT '员工ID',
  `employee_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `duty_date` date NOT NULL COMMENT '值班日期',
  `shift_type` varchar(20) NOT NULL COMMENT '班次类型：早班/晚班/全天',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `work_hours` decimal(4,1) DEFAULT NULL COMMENT '工作时长(小时)',
  `work_area` varchar(100) DEFAULT NULL COMMENT '工作区域',
  `status` varchar(20) DEFAULT 'normal' COMMENT '状态：normal/leave/swap',
  `priority` varchar(20) DEFAULT 'normal' COMMENT '优先级：normal/high',
  `notes` text COMMENT '备注信息',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_date` (`tenant_id`, `duty_date`),
  KEY `idx_employee` (`employee_id`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='珐琅馆排班记录表';
```

#### jsh_schedule_shift (班次定义表)
```sql
CREATE TABLE `jsh_schedule_shift` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shift_name` varchar(50) NOT NULL COMMENT '班次名称',
  `shift_code` varchar(20) NOT NULL COMMENT '班次编码',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `work_hours` decimal(4,1) NOT NULL COMMENT '工作时长',
  `color_code` varchar(10) DEFAULT NULL COMMENT '颜色代码',
  `description` varchar(200) DEFAULT NULL COMMENT '班次描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_code` (`tenant_id`, `shift_code`, `delete_flag`),
  KEY `idx_tenant_active` (`tenant_id`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班次定义表';
```

## 权限配置

### 菜单权限
- **功能ID**: 307
- **菜单名称**: 珐琅馆排班
- **URL路径**: /cloisonne/schedule
- **组件路径**: cloisonne/CloisonneScheduleList
- **图标**: calendar

### 按钮权限
```json
[
  {"funId": "307", "btnStr": "add,edit,delete,export,import,batch"}
]
```

### 权限说明
- `add`: 新增排班权限
- `edit`: 编辑排班权限  
- `delete`: 删除排班权限
- `export`: 导出数据权限
- `import`: 导入数据权限
- `batch`: 批量操作权限

## API接口设计

### RESTful API规范

#### 排班管理接口
```javascript
// 获取排班列表
GET /cloisonne/schedule/list?page=1&size=10&employeeName=&shiftType=&status=

// 获取排班详情
GET /cloisonne/schedule/{id}

// 新增排班
POST /cloisonne/schedule/add
{
  "employeeId": 1,
  "employeeName": "聆花老师",
  "dutyDate": "2025-06-23",
  "shiftType": "早班",
  "startTime": "09:00",
  "endTime": "13:00",
  "workArea": "展示区",
  "notes": "正常值班"
}

// 更新排班
PUT /cloisonne/schedule/update/{id}

// 删除排班
DELETE /cloisonne/schedule/delete/{id}

// 批量删除
DELETE /cloisonne/schedule/deleteBatch
{
  "ids": [1, 2, 3]
}

// 获取统计数据
GET /cloisonne/schedule/statistics?year=2025&month=6

// 导出数据
GET /cloisonne/schedule/export?startDate=2025-06-01&endDate=2025-06-30
```

#### 统一响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-06-23T10:30:00"
}
```

## 设计系统

### 颜色规范
- **主色调**: #1890ff (蓝色)
- **成功色**: #52c41a (绿色)  
- **警告色**: #faad14 (橙色)
- **错误色**: #ff4d4f (红色)
- **文本色**: #333333 (深灰)
- **辅助色**: #666666 (中灰)
- **边框色**: #e8e8e8 (浅灰)

### 班次颜色编码
- **早班**: #fa8c16 (橙色渐变)
- **晚班**: #52c41a (绿色渐变)
- **全天**: #1890ff (蓝色渐变)

### 组件规范
- **圆角**: 6px-16px
- **阴影**: 0 2px 8px rgba(0,0,0,0.1)
- **间距**: 8px, 12px, 16px, 24px
- **字体**: 12px, 14px, 16px, 18px, 24px

## 开发指南

### 本地开发环境
1. **前端开发服务器**: localhost:8080
2. **后端开发服务器**: localhost:9999  
3. **数据库**: localhost:3306 (jsh_erp)
4. **Redis**: localhost:6379

### 开发流程
1. **需求分析**: 明确功能需求和用户场景
2. **UI设计**: 基于设计系统创建界面原型
3. **组件开发**: 按模块化方式开发Vue组件
4. **API集成**: 对接后端接口，实现数据交互
5. **测试验证**: 功能测试、兼容性测试、性能测试
6. **部署上线**: 构建生产版本，部署到服务器

### 代码规范
- **命名规范**: 使用驼峰命名法，组件名使用PascalCase
- **文件组织**: 按功能模块组织文件结构
- **注释规范**: 关键逻辑添加中文注释
- **代码格式**: 使用ESLint和Prettier统一代码风格

## 性能优化

### 前端优化
- **组件懒加载**: 使用动态import实现路由级代码分割
- **图片优化**: 使用WebP格式，实现响应式图片
- **缓存策略**: 合理使用浏览器缓存和CDN
- **包大小优化**: 使用webpack-bundle-analyzer分析包大小

### 后端优化  
- **数据库索引**: 为常用查询字段建立索引
- **缓存机制**: 使用Redis缓存热点数据
- **分页查询**: 避免一次性加载大量数据
- **SQL优化**: 优化复杂查询，避免N+1问题

## 测试策略

### 单元测试
- **组件测试**: 使用Vue Test Utils测试组件功能
- **工具函数测试**: 测试日期处理、数据格式化等工具函数
- **API测试**: 测试接口调用和数据处理逻辑

### 集成测试
- **页面流程测试**: 测试完整的用户操作流程
- **数据一致性测试**: 验证前后端数据同步
- **权限测试**: 验证不同角色的权限控制

### 用户体验测试
- **响应式测试**: 在不同设备和屏幕尺寸下测试
- **性能测试**: 测试页面加载速度和交互响应时间
- **可用性测试**: 验证界面易用性和用户体验

## 部署指南

### 生产环境部署
1. **构建生产版本**: `npm run build`
2. **静态资源部署**: 将dist目录部署到Web服务器
3. **API服务部署**: 部署Spring Boot应用到应用服务器
4. **数据库初始化**: 执行SQL脚本创建表结构
5. **权限配置**: 在系统中配置菜单和按钮权限

### 监控和维护
- **日志监控**: 配置应用日志和错误监控
- **性能监控**: 监控页面加载时间和API响应时间
- **数据备份**: 定期备份数据库数据
- **版本管理**: 使用Git管理代码版本

## 未来规划

### AI智能排班功能
- **自动排班算法**: 基于员工技能、偏好和约束条件自动生成排班
- **冲突检测**: 智能检测排班冲突并提供解决方案
- **负载均衡**: 自动平衡员工工作量，优化排班公平性
- **预测分析**: 基于历史数据预测排班需求

### 移动端适配
- **响应式优化**: 进一步优化移动端用户体验
- **PWA支持**: 支持离线使用和推送通知
- **原生应用**: 开发iOS和Android原生应用

### 高级功能
- **多语言支持**: 支持中英文等多语言界面
- **主题定制**: 支持深色模式和主题定制
- **数据导入导出**: 支持Excel、CSV等格式的数据导入导出
- **报表系统**: 丰富的统计报表和数据可视化

---

**文档版本**: v1.0  
**最后更新**: 2025-06-23  
**维护人员**: Augment Code AI Assistant
