# 珐琅馆值班管理API接口文档

## 接口概述

珐琅馆值班管理模块提供完整的RESTful API接口，支持排班的增删改查、统计分析、数据导入导出等功能。所有接口遵循统一的响应格式和错误处理机制。

## 基础信息

### 接口地址
- **开发环境**: `http://localhost:9999`
- **生产环境**: `https://your-domain.com/api`

### 认证方式
- **认证类型**: JWT Token
- **请求头**: `Authorization: Bearer {token}`
- **Token获取**: 通过登录接口获取

### 通用请求头
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Tenant-Id: {tenant_id}
```

## 统一响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-06-23T10:30:00"
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": "2025-06-23T10:30:00"
}
```

### 状态码说明
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 排班管理接口

### 1. 获取排班列表

#### 接口信息
- **URL**: `/cloisonne/schedule/list`
- **方法**: `GET`
- **描述**: 分页查询排班记录列表

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| page | int | 否 | 页码，从1开始 | 1 |
| size | int | 否 | 每页大小，默认10 | 10 |
| employeeName | string | 否 | 员工姓名（模糊查询） | 聆花 |
| shiftType | string | 否 | 班次类型 | 早班 |
| status | string | 否 | 状态 | normal |
| startDate | string | 否 | 开始日期 | 2025-06-01 |
| endDate | string | 否 | 结束日期 | 2025-06-30 |

#### 请求示例
```http
GET /cloisonne/schedule/list?page=1&size=10&employeeName=聆花&shiftType=早班
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "employeeId": 18,
        "employeeName": "聆花老师",
        "dutyDate": "2025-06-23",
        "shiftType": "早班",
        "startTime": "09:00",
        "endTime": "13:00",
        "workHours": 4.0,
        "workArea": "展示区",
        "status": "normal",
        "priority": "normal",
        "notes": "正常值班",
        "createTime": "2025-06-23T08:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

### 2. 获取排班详情

#### 接口信息
- **URL**: `/cloisonne/schedule/{id}`
- **方法**: `GET`
- **描述**: 根据ID获取排班详情

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | long | 是 | 排班记录ID |

#### 请求示例
```http
GET /cloisonne/schedule/1
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "employeeId": 18,
    "employeeName": "聆花老师",
    "dutyDate": "2025-06-23",
    "shiftType": "早班",
    "startTime": "09:00",
    "endTime": "13:00",
    "workHours": 4.0,
    "workArea": "展示区",
    "status": "normal",
    "priority": "normal",
    "notes": "正常值班",
    "createTime": "2025-06-23T08:00:00",
    "updateTime": "2025-06-23T08:00:00"
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

### 3. 新增排班

#### 接口信息
- **URL**: `/cloisonne/schedule/add`
- **方法**: `POST`
- **描述**: 新增排班记录

#### 请求体
```json
{
  "employeeId": 18,
  "employeeName": "聆花老师",
  "dutyDate": "2025-06-23",
  "shiftType": "早班",
  "startTime": "09:00",
  "endTime": "13:00",
  "workArea": "展示区",
  "status": "normal",
  "priority": "normal",
  "notes": "正常值班"
}
```

#### 字段说明
| 字段名 | 类型 | 必填 | 描述 | 约束 |
|--------|------|------|------|------|
| employeeId | long | 是 | 员工ID | 必须存在 |
| employeeName | string | 是 | 员工姓名 | 最大50字符 |
| dutyDate | string | 是 | 值班日期 | YYYY-MM-DD格式 |
| shiftType | string | 是 | 班次类型 | 早班/晚班/全天 |
| startTime | string | 是 | 开始时间 | HH:mm格式 |
| endTime | string | 是 | 结束时间 | HH:mm格式 |
| workArea | string | 否 | 工作区域 | 最大100字符 |
| status | string | 否 | 状态 | 默认normal |
| priority | string | 否 | 优先级 | 默认normal |
| notes | string | 否 | 备注 | 最大500字符 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "新增成功",
  "data": {
    "id": 1
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

### 4. 更新排班

#### 接口信息
- **URL**: `/cloisonne/schedule/update/{id}`
- **方法**: `PUT`
- **描述**: 更新排班记录

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | long | 是 | 排班记录ID |

#### 请求体
```json
{
  "employeeId": 18,
  "employeeName": "聆花老师",
  "dutyDate": "2025-06-23",
  "shiftType": "全天",
  "startTime": "09:00",
  "endTime": "18:00",
  "workArea": "全区域",
  "status": "normal",
  "priority": "high",
  "notes": "调整为全天值班"
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "更新成功",
  "data": null,
  "timestamp": "2025-06-23T10:30:00"
}
```

### 5. 删除排班

#### 接口信息
- **URL**: `/cloisonne/schedule/delete/{id}`
- **方法**: `DELETE`
- **描述**: 删除单个排班记录

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | long | 是 | 排班记录ID |

#### 请求示例
```http
DELETE /cloisonne/schedule/delete/1
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "删除成功",
  "data": null,
  "timestamp": "2025-06-23T10:30:00"
}
```

### 6. 批量删除排班

#### 接口信息
- **URL**: `/cloisonne/schedule/deleteBatch`
- **方法**: `DELETE`
- **描述**: 批量删除排班记录

#### 请求体
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "批量删除成功，共删除5条记录",
  "data": {
    "deletedCount": 5
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

## 统计分析接口

### 1. 获取统计数据

#### 接口信息
- **URL**: `/cloisonne/schedule/statistics`
- **方法**: `GET`
- **描述**: 获取排班统计数据

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| year | int | 是 | 年份 | 2025 |
| month | int | 是 | 月份 | 6 |

#### 请求示例
```http
GET /cloisonne/schedule/statistics?year=2025&month=6
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "overview": {
      "totalWorkDays": 5,
      "totalEmployees": 5,
      "averageWorkDays": 1.0,
      "maxWorkDays": 1
    },
    "employeeStats": [
      {
        "employeeId": 18,
        "employeeName": "聆花老师",
        "workDays": 1,
        "totalHours": 4.0,
        "shifts": {
          "早班": 1
        }
      }
    ],
    "shiftDistribution": {
      "早班": 2,
      "晚班": 2,
      "全天": 1
    },
    "insights": {
      "workloadAnalysis": "员工工作负载较轻，可以考虑承担更多工作",
      "efficiencyAssessment": "班次类型丰富，能够灵活应对不同时段需求",
      "recommendation": "员工工作分配均衡，排班安排优秀"
    }
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

### 2. 获取员工工时统计

#### 接口信息
- **URL**: `/cloisonne/schedule/employee-hours`
- **方法**: `GET`
- **描述**: 获取员工工时统计

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| year | int | 是 | 年份 | 2025 |
| month | int | 是 | 月份 | 6 |
| employeeId | long | 否 | 员工ID | 18 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "employeeId": 18,
      "employeeName": "聆花老师",
      "totalHours": 4.0,
      "workDays": 1,
      "averageHoursPerDay": 4.0,
      "shiftBreakdown": {
        "早班": {
          "count": 1,
          "hours": 4.0
        }
      }
    }
  ],
  "timestamp": "2025-06-23T10:30:00"
}
```

## 班次管理接口

### 1. 获取班次列表

#### 接口信息
- **URL**: `/cloisonne/shift/list`
- **方法**: `GET`
- **描述**: 获取班次定义列表

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "shiftName": "早班",
      "shiftCode": "MORNING",
      "startTime": "09:00",
      "endTime": "13:00",
      "workHours": 4.0,
      "colorCode": "#fa8c16",
      "description": "上午时段值班",
      "isActive": true
    },
    {
      "id": 2,
      "shiftName": "晚班",
      "shiftCode": "EVENING",
      "startTime": "14:00",
      "endTime": "18:00",
      "workHours": 4.0,
      "colorCode": "#52c41a",
      "description": "下午时段值班",
      "isActive": true
    },
    {
      "id": 3,
      "shiftName": "全天",
      "shiftCode": "FULLDAY",
      "startTime": "09:00",
      "endTime": "18:00",
      "workHours": 8.0,
      "colorCode": "#1890ff",
      "description": "全天候值班",
      "isActive": true
    }
  ],
  "timestamp": "2025-06-23T10:30:00"
}
```

## 数据导入导出接口

### 1. 导出排班数据

#### 接口信息
- **URL**: `/cloisonne/schedule/export`
- **方法**: `GET`
- **描述**: 导出排班数据为Excel文件

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| startDate | string | 是 | 开始日期 | 2025-06-01 |
| endDate | string | 是 | 结束日期 | 2025-06-30 |
| format | string | 否 | 导出格式 | excel |

#### 请求示例
```http
GET /cloisonne/schedule/export?startDate=2025-06-01&endDate=2025-06-30&format=excel
```

#### 响应
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Content-Disposition**: `attachment; filename="schedule_export_20250623.xlsx"`

### 2. 导入排班数据

#### 接口信息
- **URL**: `/cloisonne/schedule/import`
- **方法**: `POST`
- **描述**: 从Excel文件导入排班数据
- **Content-Type**: `multipart/form-data`

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | file | 是 | Excel文件 |
| overwrite | boolean | 否 | 是否覆盖已存在数据 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "导入成功",
  "data": {
    "totalRows": 100,
    "successRows": 95,
    "failedRows": 5,
    "errors": [
      {
        "row": 10,
        "message": "员工不存在"
      },
      {
        "row": 25,
        "message": "日期格式错误"
      }
    ]
  },
  "timestamp": "2025-06-23T10:30:00"
}
```

## 错误码说明

### 业务错误码
| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 40001 | 员工不存在 | 检查员工ID是否正确 |
| 40002 | 排班时间冲突 | 检查同一员工同一天是否已有排班 |
| 40003 | 班次类型无效 | 使用有效的班次类型 |
| 40004 | 日期格式错误 | 使用YYYY-MM-DD格式 |
| 40005 | 时间格式错误 | 使用HH:mm格式 |
| 40006 | 工作时间无效 | 结束时间必须晚于开始时间 |
| 40007 | 权限不足 | 联系管理员分配相应权限 |

### 系统错误码
| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 50001 | 数据库连接失败 | 检查数据库配置和连接 |
| 50002 | 缓存服务异常 | 检查Redis服务状态 |
| 50003 | 文件上传失败 | 检查文件大小和格式 |
| 50004 | 数据导出失败 | 检查磁盘空间和权限 |

## 接口调用示例

### JavaScript (Axios)
```javascript
// 获取排班列表
const getScheduleList = async (params) => {
  try {
    const response = await axios.get('/cloisonne/schedule/list', {
      params,
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant-Id': tenantId
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取排班列表失败:', error);
    throw error;
  }
};

// 新增排班
const addSchedule = async (data) => {
  try {
    const response = await axios.post('/cloisonne/schedule/add', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant-Id': tenantId,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('新增排班失败:', error);
    throw error;
  }
};
```

### Java (Spring RestTemplate)
```java
// 获取排班列表
public ResponseEntity<String> getScheduleList(Map<String, Object> params) {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer " + token);
    headers.set("X-Tenant-Id", tenantId);
    
    HttpEntity<String> entity = new HttpEntity<>(headers);
    
    UriComponentsBuilder builder = UriComponentsBuilder
        .fromHttpUrl(baseUrl + "/cloisonne/schedule/list");
    
    params.forEach(builder::queryParam);
    
    return restTemplate.exchange(
        builder.toUriString(),
        HttpMethod.GET,
        entity,
        String.class
    );
}
```

### Python (Requests)
```python
import requests

# 获取排班列表
def get_schedule_list(params):
    headers = {
        'Authorization': f'Bearer {token}',
        'X-Tenant-Id': tenant_id
    }
    
    response = requests.get(
        f'{base_url}/cloisonne/schedule/list',
        params=params,
        headers=headers
    )
    
    return response.json()

# 新增排班
def add_schedule(data):
    headers = {
        'Authorization': f'Bearer {token}',
        'X-Tenant-Id': tenant_id,
        'Content-Type': 'application/json'
    }
    
    response = requests.post(
        f'{base_url}/cloisonne/schedule/add',
        json=data,
        headers=headers
    )
    
    return response.json()
```

## 版本更新记录

### v1.0 (2025-06-23)
- 初始版本发布
- 实现基础的排班CRUD接口
- 提供统计分析功能
- 支持数据导入导出

---

**API文档版本**: v1.0  
**最后更新**: 2025-06-23  
**维护人员**: Augment Code AI Assistant
