# 珐琅馆智能排班系统部署运维指南

## 目录
1. [环境要求](#环境要求)
2. [部署步骤](#部署步骤)
3. [配置说明](#配置说明)
4. [数据库初始化](#数据库初始化)
5. [系统启动](#系统启动)
6. [功能验证](#功能验证)
7. [性能优化](#性能优化)
8. [监控运维](#监控运维)
9. [故障排除](#故障排除)
10. [升级维护](#升级维护)

## 环境要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 100GB以上可用空间
- **网络**: 稳定的网络连接

### 软件环境
- **操作系统**: Linux (CentOS 7+/Ubuntu 18+) 或 Windows Server 2016+
- **Java**: JDK 1.8 或 OpenJDK 8+
- **Node.js**: 14.x 或更高版本
- **MySQL**: 5.7.33 或更高版本
- **Redis**: 6.0+ 
- **Nginx**: 1.18+ (可选，用于反向代理)

### 开发工具（开发环境）
- **IDE**: IntelliJ IDEA 或 Eclipse
- **构建工具**: Maven 3.6+
- **版本控制**: Git 2.0+

## 部署步骤

### 第一步：环境准备

#### 1.1 安装Java环境
```bash
# CentOS/RHEL
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu/Debian
sudo apt-get update
sudo apt-get install openjdk-8-jdk

# 验证安装
java -version
javac -version
```

#### 1.2 安装Node.js
```bash
# 使用NodeSource仓库安装
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 1.3 安装MySQL
```bash
# CentOS/RHEL
sudo yum install mysql-server mysql

# Ubuntu/Debian
sudo apt-get install mysql-server mysql-client

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 1.4 安装Redis
```bash
# CentOS/RHEL
sudo yum install redis

# Ubuntu/Debian
sudo apt-get install redis-server

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### 第二步：代码部署

#### 2.1 获取源代码
```bash
# 克隆代码仓库
git clone [repository-url]
cd jshERP-0612-Cursor

# 或者解压部署包
tar -xzf jsherp-schedule-system.tar.gz
cd jsherp-schedule-system
```

#### 2.2 后端部署
```bash
# 进入后端目录
cd jshERP-boot

# 编译打包
mvn clean package -Dmaven.test.skip=true

# 检查生成的JAR文件
ls -la jeecg-boot-module-system/target/*.jar
```

#### 2.3 前端部署
```bash
# 进入前端目录
cd jshERP-web

# 安装依赖
npm install

# 构建生产版本
npm run build

# 检查构建结果
ls -la dist/
```

### 第三步：配置文件设置

#### 3.1 后端配置
编辑 `jshERP-boot/jeecg-boot-module-system/src/main/resources/application.yml`：

```yaml
server:
  port: 9999
  servlet:
    context-path: /jeecg-boot

spring:
  datasource:
    master:
      url: **********************************************************************************************************************************************************************
      username: jsh_user
      password: 123456
      driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    jedis:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# AI排班算法配置
ai:
  schedule:
    algorithm:
      population-size: 100
      max-generations: 500
      mutation-rate: 0.1
      crossover-rate: 0.8
      convergence-threshold: 0.01
    performance:
      max-execution-time: 30000
      max-memory-usage: 100
      enable-caching: true
      enable-parallel: false
```

#### 3.2 前端配置
编辑 `jshERP-web/src/config/index.js`：

```javascript
export default {
  // API基础URL
  apiBaseUrl: process.env.NODE_ENV === 'production' 
    ? 'http://your-domain.com:9999/jeecg-boot' 
    : 'http://localhost:9999/jeecg-boot',
  
  // 系统配置
  system: {
    title: '珐琅馆智能排班系统',
    version: '1.0.0'
  },
  
  // AI排班配置
  aiSchedule: {
    defaultConfig: {
      populationSize: 100,
      maxGenerations: 500,
      objectives: {
        coverage: 40,
        fairness: 30,
        satisfaction: 20,
        costEfficiency: 10
      }
    }
  }
}
```

## 数据库初始化

### 第一步：创建数据库
```sql
-- 创建数据库
CREATE DATABASE jsh_erp DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'jsh_user'@'localhost' IDENTIFIED BY '123456';
CREATE USER 'jsh_user'@'%' IDENTIFIED BY '123456';

-- 授权
GRANT ALL PRIVILEGES ON jsh_erp.* TO 'jsh_user'@'localhost';
GRANT ALL PRIVILEGES ON jsh_erp.* TO 'jsh_user'@'%';
FLUSH PRIVILEGES;
```

### 第二步：导入基础数据
```bash
# 导入jshERP基础表结构和数据
mysql -u jsh_user -p123456 jsh_erp < sql/jsh_erp_base.sql

# 导入排班系统相关表结构
mysql -u jsh_user -p123456 jsh_erp < sql/cloisonne_schedule.sql

# 导入AI排班相关表结构
mysql -u jsh_user -p123456 jsh_erp < sql/ai_schedule.sql
```

### 第三步：初始化排班数据
```sql
-- 插入排班功能菜单
INSERT INTO jsh_function (id, number, name, pnumber, url, component, path, redirect, title, icon, type, enabled, sort, create_time, creator, update_time, updater, remark) VALUES
(310, '0310', '掐丝珐琅馆', '03', '', 'RouteView', '/cloisonne', '', '掐丝珐琅馆', 'gold', 'route', 1, 10, NOW(), 1, NOW(), 1, '掐丝珐琅馆管理'),
(31001, '031001', '珐琅馆排班', '0310', '/cloisonne/schedule', 'cloisonne/CloisonneScheduleList', '/cloisonne/schedule', '', '珐琅馆排班', 'schedule', 'route', 1, 1, NOW(), 1, NOW(), 1, '珐琅馆排班管理');

-- 插入默认员工数据
INSERT INTO jsh_person (id, type, name, enabled, create_time, creator, update_time, updater, tenant_id) VALUES
(18, '员工', '聆花老师', 1, NOW(), 1, NOW(), 1, 63),
(19, '员工', '梁朝伟', 1, NOW(), 1, NOW(), 1, 63),
(20, '员工', '龚锦华', 1, NOW(), 1, NOW(), 1, 63),
(21, '员工', '伍尚明', 1, NOW(), 1, NOW(), 1, 63),
(22, '员工', '莫智华', 1, NOW(), 1, NOW(), 1, 63);
```

## 系统启动

### 第一步：启动后端服务
```bash
# 方式1：直接运行JAR文件
cd jshERP-boot/jeecg-boot-module-system/target
java -jar jeecg-boot-module-system-2.4.6.jar

# 方式2：使用启动脚本
cd jshERP-boot
chmod +x start.sh
./start.sh

# 方式3：后台运行
nohup java -jar jeecg-boot-module-system-2.4.6.jar > logs/app.log 2>&1 &
```

### 第二步：启动前端服务

#### 开发模式
```bash
cd jshERP-web
npm run serve
```

#### 生产模式（使用Nginx）
```bash
# 复制构建文件到Nginx目录
sudo cp -r dist/* /var/www/html/

# 配置Nginx
sudo vim /etc/nginx/sites-available/jsherp-schedule
```

Nginx配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /jeecg-boot/ {
        proxy_pass http://localhost:9999/jeecg-boot/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 功能验证

### 第一步：基础功能验证
1. **登录验证**
   - 访问 http://localhost:8080
   - 使用默认账号登录：admin/123456

2. **菜单验证**
   - 检查左侧菜单是否显示"掐丝珐琅馆"
   - 点击"珐琅馆排班"是否能正常打开

3. **排班功能验证**
   - 创建新的排班记录
   - 编辑现有排班记录
   - 删除排班记录
   - 查看排班列表和日历视图

### 第二步：AI功能验证
1. **AI排班界面**
   - 切换到"AI智能排班"选项卡
   - 检查配置界面是否正常显示

2. **配置功能**
   - 设置时间范围和员工
   - 配置约束条件
   - 调整优化目标权重

3. **生成功能**
   - 点击"开始生成"
   - 观察进度显示是否正常
   - 检查生成结果是否正确

### 第三步：性能验证
1. **响应时间**
   - 页面加载时间 < 3秒
   - API响应时间 < 1秒
   - AI排班生成时间 < 30秒

2. **并发测试**
   - 多用户同时访问
   - 多个AI排班任务并行

## 性能优化

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_schedule_date ON jsh_cloisonne_schedule(schedule_date, tenant_id);
CREATE INDEX idx_schedule_employee ON jsh_cloisonne_schedule(employee_id, tenant_id);
CREATE INDEX idx_ai_task_status ON jsh_ai_schedule_task(status, tenant_id);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = 268435456; -- 256MB
```

### 应用优化
```yaml
# JVM参数优化
JAVA_OPTS: "-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 连接池优化
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### Redis优化
```conf
# redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 监控运维

### 日志管理
```bash
# 应用日志
tail -f logs/jeecg-boot.log

# 错误日志
tail -f logs/error.log

# AI排班日志
tail -f logs/ai-schedule.log

# 日志轮转配置
logrotate -d /etc/logrotate.d/jsherp
```

### 性能监控
```bash
# 系统资源监控
top
htop
iostat -x 1
free -h

# 应用监控
jps -l
jstat -gc [pid] 1s
jmap -histo [pid]

# 数据库监控
mysqladmin -u root -p processlist
mysqladmin -u root -p status
```

### 备份策略
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="jsh_erp"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u jsh_user -p123456 $DB_NAME > $BACKUP_DIR/jsh_erp_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/jsh_erp_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: jsh_erp_$DATE.sql.gz"
```

## 故障排除

### 常见问题

#### 1. 启动失败
**症状**: 应用无法启动
**排查步骤**:
```bash
# 检查端口占用
netstat -tlnp | grep 9999

# 检查Java进程
jps -l

# 查看启动日志
tail -f logs/jeecg-boot.log
```

#### 2. 数据库连接失败
**症状**: 无法连接数据库
**排查步骤**:
```bash
# 测试数据库连接
mysql -u jsh_user -p123456 -h localhost jsh_erp

# 检查MySQL服务状态
systemctl status mysql

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

#### 3. AI排班生成失败
**症状**: AI排班无法生成或生成失败
**排查步骤**:
```bash
# 检查内存使用
free -h

# 查看AI排班日志
tail -f logs/ai-schedule.log

# 检查算法参数配置
grep -r "ai.schedule" application.yml
```

#### 4. 前端页面无法访问
**症状**: 前端页面加载失败
**排查步骤**:
```bash
# 检查Nginx状态
systemctl status nginx

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log

# 检查静态文件
ls -la /var/www/html/
```

### 性能问题诊断
```bash
# CPU使用率过高
top -p [java_pid]
jstack [java_pid] > thread_dump.txt

# 内存使用过高
jmap -dump:format=b,file=heap_dump.hprof [java_pid]

# 数据库慢查询
mysql -u root -p -e "SHOW PROCESSLIST;"
mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log%';"
```

## 升级维护

### 版本升级流程
1. **备份数据**
   ```bash
   # 备份数据库
   mysqldump -u jsh_user -p123456 jsh_erp > backup_before_upgrade.sql
   
   # 备份应用文件
   tar -czf app_backup_$(date +%Y%m%d).tar.gz jshERP-boot/ jshERP-web/
   ```

2. **停止服务**
   ```bash
   # 停止应用
   pkill -f jeecg-boot-module-system
   
   # 停止Nginx
   systemctl stop nginx
   ```

3. **更新代码**
   ```bash
   # 拉取最新代码
   git pull origin main
   
   # 重新构建
   cd jshERP-boot && mvn clean package -Dmaven.test.skip=true
   cd ../jshERP-web && npm run build
   ```

4. **数据库升级**
   ```bash
   # 执行升级脚本
   mysql -u jsh_user -p123456 jsh_erp < sql/upgrade_v1.1.sql
   ```

5. **启动服务**
   ```bash
   # 启动应用
   cd jshERP-boot/jeecg-boot-module-system/target
   nohup java -jar jeecg-boot-module-system-2.4.6.jar > logs/app.log 2>&1 &
   
   # 启动Nginx
   systemctl start nginx
   ```

6. **验证升级**
   - 检查应用是否正常启动
   - 验证核心功能是否正常
   - 检查AI排班功能是否正常

### 定期维护任务
- **每日**: 检查系统运行状态、备份数据库
- **每周**: 清理日志文件、检查磁盘空间
- **每月**: 更新系统补丁、优化数据库
- **每季度**: 性能评估、容量规划

---

**技术支持**: 如有问题请联系系统管理员  
**文档版本**: v1.0  
**最后更新**: 2025-06-23
