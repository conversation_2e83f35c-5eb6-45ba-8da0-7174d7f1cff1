/**
 * AI智能排班功能测试套件
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */

import ScheduleOptimizer from '../utils/ScheduleOptimizer'
import moment from 'moment'

class AIScheduleTest {
  constructor() {
    this.testResults = []
    this.optimizer = null
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始AI智能排班功能测试...')
    
    try {
      // 基础功能测试
      await this.testBasicFunctionality()
      
      // 算法性能测试
      await this.testAlgorithmPerformance()
      
      // 约束验证测试
      await this.testConstraintValidation()
      
      // 优化指标测试
      await this.testOptimizationMetrics()
      
      // 边界条件测试
      await this.testEdgeCases()
      
      // 生成测试报告
      this.generateTestReport()
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error)
      this.addTestResult('测试执行', false, error.message)
    }
  }

  /**
   * 基础功能测试
   */
  async testBasicFunctionality() {
    console.log('📋 测试基础功能...')
    
    try {
      // 测试优化器创建
      this.optimizer = new ScheduleOptimizer({
        populationSize: 50,
        maxGenerations: 100
      })
      this.addTestResult('优化器创建', true, '成功创建优化器实例')
      
      // 测试配置设置
      const config = this.createTestConfig()
      this.optimizer.setConfig(config)
      this.addTestResult('配置设置', true, '成功设置优化配置')
      
      // 测试种群初始化
      await this.optimizer.initializePopulation()
      const hasPopulation = this.optimizer.population && this.optimizer.population.length > 0
      this.addTestResult('种群初始化', hasPopulation, hasPopulation ? '成功初始化种群' : '种群初始化失败')
      
    } catch (error) {
      this.addTestResult('基础功能测试', false, error.message)
    }
  }

  /**
   * 算法性能测试
   */
  async testAlgorithmPerformance() {
    console.log('⚡ 测试算法性能...')
    
    try {
      const startTime = Date.now()
      
      // 设置性能测试回调
      let progressCount = 0
      this.optimizer.onProgress = (progress) => {
        progressCount++
      }
      
      // 运行优化算法
      const result = await this.optimizer.optimize()
      
      const endTime = Date.now()
      const executionTime = endTime - startTime
      
      // 验证性能指标
      const performanceOK = executionTime < 30000 // 30秒内完成
      this.addTestResult('算法执行时间', performanceOK, `执行时间: ${executionTime}ms`)
      
      const progressOK = progressCount > 0
      this.addTestResult('进度回调', progressOK, `进度回调次数: ${progressCount}`)
      
      const resultOK = result && result.schedule && result.metrics
      this.addTestResult('结果完整性', resultOK, resultOK ? '结果包含完整数据' : '结果数据不完整')
      
    } catch (error) {
      this.addTestResult('算法性能测试', false, error.message)
    }
  }

  /**
   * 约束验证测试
   */
  async testConstraintValidation() {
    console.log('🔒 测试约束验证...')
    
    try {
      // 创建带约束的配置
      const config = this.createConstrainedConfig()
      this.optimizer.setConfig(config)
      
      // 运行优化
      const result = await this.optimizer.optimize()
      
      // 验证约束满足情况
      const constraintResults = this.validateConstraints(result.schedule, config.constraints)
      
      constraintResults.forEach(constraint => {
        this.addTestResult(
          `约束验证-${constraint.name}`, 
          constraint.satisfied, 
          constraint.message
        )
      })
      
    } catch (error) {
      this.addTestResult('约束验证测试', false, error.message)
    }
  }

  /**
   * 优化指标测试
   */
  async testOptimizationMetrics() {
    console.log('📊 测试优化指标...')
    
    try {
      const config = this.createTestConfig()
      this.optimizer.setConfig(config)
      
      const result = await this.optimizer.optimize()
      const metrics = result.metrics
      
      // 验证指标范围
      const coverageOK = metrics.coverage >= 0 && metrics.coverage <= 100
      this.addTestResult('覆盖率指标', coverageOK, `覆盖率: ${metrics.coverage}%`)
      
      const fairnessOK = metrics.fairness >= 0 && metrics.fairness <= 100
      this.addTestResult('公平性指标', fairnessOK, `公平性: ${metrics.fairness}%`)
      
      const satisfactionOK = metrics.satisfaction >= 0 && metrics.satisfaction <= 100
      this.addTestResult('满意度指标', satisfactionOK, `满意度: ${metrics.satisfaction}%`)
      
      const efficiencyOK = metrics.costEfficiency >= 0 && metrics.costEfficiency <= 100
      this.addTestResult('成本效率指标', efficiencyOK, `成本效率: ${metrics.costEfficiency}%`)
      
      // 验证适应度计算
      const fitnessOK = result.fitness >= 0 && result.fitness <= 100
      this.addTestResult('适应度计算', fitnessOK, `适应度: ${result.fitness}`)
      
    } catch (error) {
      this.addTestResult('优化指标测试', false, error.message)
    }
  }

  /**
   * 边界条件测试
   */
  async testEdgeCases() {
    console.log('🎯 测试边界条件...')
    
    try {
      // 测试空员工列表
      await this.testEmptyEmployees()
      
      // 测试单员工情况
      await this.testSingleEmployee()
      
      // 测试极端约束
      await this.testExtremeConstraints()
      
      // 测试停止功能
      await this.testStopFunctionality()
      
    } catch (error) {
      this.addTestResult('边界条件测试', false, error.message)
    }
  }

  /**
   * 测试空员工列表
   */
  async testEmptyEmployees() {
    try {
      const config = this.createTestConfig()
      config.employees = []
      
      this.optimizer.setConfig(config)
      
      // 应该抛出错误或返回空结果
      try {
        await this.optimizer.optimize()
        this.addTestResult('空员工列表', false, '应该处理空员工列表错误')
      } catch (error) {
        this.addTestResult('空员工列表', true, '正确处理空员工列表')
      }
    } catch (error) {
      this.addTestResult('空员工列表测试', false, error.message)
    }
  }

  /**
   * 测试单员工情况
   */
  async testSingleEmployee() {
    try {
      const config = this.createTestConfig()
      config.employees = [config.employees[0]] // 只保留一个员工
      
      this.optimizer.setConfig(config)
      const result = await this.optimizer.optimize()
      
      const singleEmployeeOK = result && result.schedule
      this.addTestResult('单员工排班', singleEmployeeOK, '成功处理单员工情况')
      
    } catch (error) {
      this.addTestResult('单员工测试', false, error.message)
    }
  }

  /**
   * 测试极端约束
   */
  async testExtremeConstraints() {
    try {
      const config = this.createTestConfig()
      
      // 设置极端约束：所有班次都需要5个人，但只有3个员工
      config.constraints.time.minStaffPerShift = {
        '早班': 5,
        '晚班': 5,
        '全天': 5
      }
      
      this.optimizer.setConfig(config)
      const result = await this.optimizer.optimize()
      
      // 应该能处理不可满足的约束
      const extremeConstraintOK = result && result.metrics.coverage < 100
      this.addTestResult('极端约束处理', extremeConstraintOK, '正确处理不可满足约束')
      
    } catch (error) {
      this.addTestResult('极端约束测试', false, error.message)
    }
  }

  /**
   * 测试停止功能
   */
  async testStopFunctionality() {
    try {
      const config = this.createTestConfig()
      this.optimizer.setConfig(config)
      
      // 启动优化
      const optimizePromise = this.optimizer.optimize()
      
      // 延迟后停止
      setTimeout(() => {
        this.optimizer.stop()
      }, 1000)
      
      try {
        await optimizePromise
        this.addTestResult('停止功能', false, '停止功能未正确工作')
      } catch (error) {
        const stopOK = error.message.includes('停止')
        this.addTestResult('停止功能', stopOK, '停止功能正常工作')
      }
      
    } catch (error) {
      this.addTestResult('停止功能测试', false, error.message)
    }
  }

  /**
   * 创建测试配置
   */
  createTestConfig() {
    return {
      objectives: {
        coverage: 40,
        fairness: 30,
        satisfaction: 20,
        costEfficiency: 10
      },
      constraints: {
        employee: {
          availability: {},
          preferences: {}
        },
        time: {
          minStaffPerShift: {
            '早班': 1,
            '晚班': 1,
            '全天': 1
          },
          maxStaffPerShift: {
            '早班': 3,
            '晚班': 3,
            '全天': 2
          }
        },
        business: {
          specialEvents: [],
          holidays: []
        }
      },
      employees: [
        { id: 1, name: '员工A' },
        { id: 2, name: '员工B' },
        { id: 3, name: '员工C' }
      ],
      shifts: [
        { code: 'morning', name: '早班', startTime: '09:00', endTime: '13:00' },
        { code: 'evening', name: '晚班', startTime: '14:00', endTime: '18:00' },
        { code: 'fullday', name: '全天', startTime: '09:00', endTime: '18:00' }
      ],
      dateRange: [
        moment().add(1, 'day'),
        moment().add(7, 'days')
      ]
    }
  }

  /**
   * 创建带约束的配置
   */
  createConstrainedConfig() {
    const config = this.createTestConfig()
    
    // 添加员工约束
    config.constraints.employee.availability = {
      1: {
        monday: { available: true, start: moment('09:00', 'HH:mm'), end: moment('17:00', 'HH:mm') },
        tuesday: { available: false }
      }
    }
    
    config.constraints.employee.preferences = {
      1: {
        preferredShifts: ['早班'],
        avoidShifts: ['全天'],
        maxConsecutiveDays: 3
      }
    }
    
    return config
  }

  /**
   * 验证约束满足情况
   */
  validateConstraints(schedule, constraints) {
    const results = []
    
    // 验证最少人数约束
    const minStaffResults = this.validateMinStaffConstraints(schedule, constraints.time.minStaffPerShift)
    results.push(...minStaffResults)
    
    // 验证员工可用性约束
    const availabilityResults = this.validateAvailabilityConstraints(schedule, constraints.employee.availability)
    results.push(...availabilityResults)
    
    return results
  }

  /**
   * 验证最少人数约束
   */
  validateMinStaffConstraints(schedule, minStaffPerShift) {
    const results = []
    
    Object.entries(schedule).forEach(([date, daySchedule]) => {
      Object.entries(daySchedule).forEach(([shiftCode, employees]) => {
        const shiftName = this.getShiftNameByCode(shiftCode)
        const minRequired = minStaffPerShift[shiftName] || 1
        const actualStaff = employees.length
        
        results.push({
          name: `最少人数-${date}-${shiftName}`,
          satisfied: actualStaff >= minRequired,
          message: `需要${minRequired}人，实际${actualStaff}人`
        })
      })
    })
    
    return results
  }

  /**
   * 验证员工可用性约束
   */
  validateAvailabilityConstraints(schedule, availability) {
    const results = []
    
    Object.entries(schedule).forEach(([date, daySchedule]) => {
      const dayOfWeek = moment(date).format('dddd').toLowerCase()
      
      Object.values(daySchedule).forEach(employees => {
        employees.forEach(emp => {
          const empAvailability = availability[emp.id]?.[dayOfWeek]
          
          if (empAvailability && !empAvailability.available) {
            results.push({
              name: `可用性-${emp.name}-${date}`,
              satisfied: false,
              message: `员工${emp.name}在${date}不可用`
            })
          } else {
            results.push({
              name: `可用性-${emp.name}-${date}`,
              satisfied: true,
              message: `员工${emp.name}可用性正常`
            })
          }
        })
      })
    })
    
    return results
  }

  /**
   * 根据班次代码获取班次名称
   */
  getShiftNameByCode(code) {
    const mapping = {
      'morning': '早班',
      'evening': '晚班',
      'fullday': '全天'
    }
    return mapping[code] || code
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message,
      timestamp: new Date()
    })
    
    const status = passed ? '✅' : '❌'
    console.log(`${status} ${testName}: ${message}`)
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    const passRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0
    
    console.log('\n📊 测试报告')
    console.log('=' * 50)
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过: ${passedTests}`)
    console.log(`失败: ${failedTests}`)
    console.log(`通过率: ${passRate}%`)
    console.log('=' * 50)
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`- ${r.name}: ${r.message}`)
        })
    }
    
    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      passRate: parseFloat(passRate),
      details: this.testResults
    }
  }
}

export default AIScheduleTest
