<template>
  <div class="schedule-templates">
    <div class="templates-header">
      <h3>排班模板管理</h3>
      <p>创建和管理可重复使用的排班模板</p>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" icon="plus" @click="showCreateTemplate">
            创建模板
          </a-button>
          <a-button icon="import" @click="importTemplates">
            导入模板
          </a-button>
          <a-button icon="export" @click="exportTemplates">
            导出模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 模板分类标签 -->
    <div class="template-categories">
      <a-tabs v-model="activeCategory" @change="handleCategoryChange">
        <a-tab-pane key="all" tab="全部模板" />
        <a-tab-pane key="standard" tab="标准模板" />
        <a-tab-pane key="holiday" tab="节假日模板" />
        <a-tab-pane key="custom" tab="自定义模板" />
        <a-tab-pane key="industry" tab="行业模板" />
      </a-tabs>
    </div>

    <!-- 模板列表 -->
    <div class="templates-grid">
      <a-card
        v-for="template in filteredTemplates"
        :key="template.id"
        class="template-card"
        :class="{ 'template-selected': selectedTemplates.includes(template.id) }"
        @click="selectTemplate(template)"
      >
        <div class="template-header">
          <div class="template-info">
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
          </div>
          <div class="template-actions">
            <a-dropdown>
              <a-button type="link" icon="more" />
              <a-menu slot="overlay">
                <a-menu-item @click="editTemplate(template)">
                  <a-icon type="edit" /> 编辑
                </a-menu-item>
                <a-menu-item @click="duplicateTemplate(template)">
                  <a-icon type="copy" /> 复制
                </a-menu-item>
                <a-menu-item @click="previewTemplate(template)">
                  <a-icon type="eye" /> 预览
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="deleteTemplate(template)" style="color: #ff4d4f">
                  <a-icon type="delete" /> 删除
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
        </div>

        <div class="template-content">
          <!-- 模板标签 -->
          <div class="template-tags">
            <a-tag :color="getCategoryColor(template.category)">
              {{ getCategoryLabel(template.category) }}
            </a-tag>
            <a-tag v-if="template.isDefault" color="gold">
              默认模板
            </a-tag>
            <a-tag v-if="template.isPublic" color="green">
              公共模板
            </a-tag>
          </div>

          <!-- 模板统计 -->
          <div class="template-stats">
            <a-row :gutter="8">
              <a-col :span="8">
                <a-statistic
                  title="适用天数"
                  :value="template.duration"
                  suffix="天"
                  :value-style="{ fontSize: '14px' }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="班次数"
                  :value="template.shiftCount"
                  :value-style="{ fontSize: '14px' }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="使用次数"
                  :value="template.usageCount"
                  :value-style="{ fontSize: '14px' }"
                />
              </a-col>
            </a-row>
          </div>

          <!-- 模板预览 -->
          <div class="template-preview">
            <div class="preview-header">排班预览</div>
            <div class="preview-grid">
              <div
                v-for="day in template.previewDays"
                :key="day.date"
                class="preview-day"
              >
                <div class="day-label">{{ day.label }}</div>
                <div class="day-shifts">
                  <div
                    v-for="shift in day.shifts"
                    :key="shift.id"
                    class="shift-block"
                    :style="{ backgroundColor: shift.color }"
                  >
                    {{ shift.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模板操作 -->
          <div class="template-footer">
            <a-space>
              <a-button size="small" @click="applyTemplate(template)">
                应用模板
              </a-button>
              <a-button size="small" @click="editTemplate(template)">
                编辑
              </a-button>
            </a-space>
            <div class="template-meta">
              <span>创建于 {{ formatDate(template.createTime) }}</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'ScheduleTemplates',

  data() {
    return {
      // 当前分类
      activeCategory: 'all',

      // 选中的模板
      selectedTemplates: [],

      // 模板数据
      templates: [
        {
          id: 1,
          name: '标准工作日排班',
          description: '适用于周一到周五的标准工作日排班',
          category: 'standard',
          duration: 7,
          shiftCount: 3,
          usageCount: 25,
          isDefault: true,
          isPublic: true,
          createTime: new Date('2025-06-01'),
          previewDays: [
            {
              date: '2025-06-23',
              label: '周一',
              shifts: [
                { id: 1, name: '早班', color: '#fa8c16' },
                { id: 2, name: '晚班', color: '#52c41a' }
              ]
            },
            {
              date: '2025-06-24',
              label: '周二',
              shifts: [
                { id: 1, name: '早班', color: '#fa8c16' },
                { id: 2, name: '晚班', color: '#52c41a' }
              ]
            }
          ]
        },
        {
          id: 2,
          name: '节假日排班',
          description: '适用于节假日期间的特殊排班安排',
          category: 'holiday',
          duration: 3,
          shiftCount: 2,
          usageCount: 8,
          isDefault: false,
          isPublic: true,
          createTime: new Date('2025-06-10'),
          previewDays: [
            {
              date: '2025-06-23',
              label: '节假日',
              shifts: [
                { id: 1, name: '全天', color: '#1890ff' }
              ]
            }
          ]
        }
      ]
    }
  },

  computed: {
    filteredTemplates() {
      if (this.activeCategory === 'all') {
        return this.templates
      }
      return this.templates.filter(template => template.category === this.activeCategory)
    }
  },

  methods: {
    // 处理分类切换
    handleCategoryChange(category) {
      this.activeCategory = category
    },

    // 选择模板
    selectTemplate(template) {
      const index = this.selectedTemplates.indexOf(template.id)
      if (index > -1) {
        this.selectedTemplates.splice(index, 1)
      } else {
        this.selectedTemplates.push(template.id)
      }
    },

    // 获取分类颜色
    getCategoryColor(category) {
      const colors = {
        standard: 'blue',
        holiday: 'orange',
        custom: 'purple',
        industry: 'green'
      }
      return colors[category] || 'default'
    },

    // 获取分类标签
    getCategoryLabel(category) {
      const labels = {
        standard: '标准',
        holiday: '节假日',
        custom: '自定义',
        industry: '行业'
      }
      return labels[category] || category
    },

    // 格式化日期
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    // 显示创建模板弹窗
    showCreateTemplate() {
      this.$message.info('创建模板功能开发中...')
    },

    // 编辑模板
    editTemplate(template) {
      this.$message.info('编辑模板功能开发中...')
    },

    // 复制模板
    duplicateTemplate(template) {
      const newTemplate = {
        ...template,
        id: Date.now(),
        name: template.name + ' (副本)',
        usageCount: 0,
        createTime: new Date()
      }
      this.templates.push(newTemplate)
      this.$message.success('模板复制成功')
    },

    // 预览模板
    previewTemplate(template) {
      this.$message.info('预览模板功能开发中...')
    },

    // 删除模板
    deleteTemplate(template) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除模板"${template.name}"吗？`,
        onOk: () => {
          const index = this.templates.findIndex(t => t.id === template.id)
          if (index > -1) {
            this.templates.splice(index, 1)
            this.$message.success('模板删除成功')
          }
        }
      })
    },

    // 应用模板
    applyTemplate(template) {
      this.$confirm({
        title: '应用模板',
        content: `确定要应用模板"${template.name}"吗？`,
        onOk: () => {
          // 触发应用模板事件
          this.$emit('apply-template', template)
          this.$message.success('模板应用成功')
        }
      })
    },

    // 导入模板
    importTemplates() {
      this.$message.info('导入功能开发中...')
    },

    // 导出模板
    exportTemplates() {
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.schedule-templates {
  padding: 20px;
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.templates-header h3 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.templates-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.template-categories {
  margin-bottom: 24px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.template-card.template-selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.template-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.template-info p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.template-stats {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.template-preview {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.preview-header {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.preview-day {
  display: flex;
  align-items: center;
  gap: 8px;
}

.day-label {
  width: 40px;
  font-size: 11px;
  color: #666;
}

.day-shifts {
  display: flex;
  gap: 4px;
  flex: 1;
}

.shift-block {
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 10px;
  font-weight: 500;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.template-meta {
  font-size: 11px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .templates-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .template-header {
    flex-direction: column;
    gap: 8px;
  }

  .template-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>