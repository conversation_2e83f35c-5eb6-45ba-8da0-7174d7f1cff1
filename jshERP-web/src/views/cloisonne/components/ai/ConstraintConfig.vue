<template>
  <div class="constraint-config">
    <a-tabs v-model="activeTab" size="small">
      <!-- 员工约束 -->
      <a-tab-pane key="employee" tab="员工约束">
        <div class="constraint-section">
          <h5>员工可用时间</h5>
          <div class="employee-availability">
            <div 
              v-for="employee in employees" 
              :key="employee.id"
              class="employee-item"
            >
              <div class="employee-header">
                <a-avatar :size="24" :style="{ backgroundColor: getAvatarColor(employee.name) }">
                  {{ employee.name.charAt(0) }}
                </a-avatar>
                <span class="employee-name">{{ employee.name }}</span>
                <a-switch 
                  v-model="constraints.employee.availability[employee.id].enabled"
                  size="small"
                />
              </div>
              
              <div 
                v-if="constraints.employee.availability[employee.id].enabled"
                class="availability-config"
              >
                <a-row :gutter="8">
                  <a-col 
                    v-for="day in weekDays" 
                    :key="day.key"
                    :span="3"
                  >
                    <div class="day-config">
                      <div class="day-label">{{ day.label }}</div>
                      <a-checkbox 
                        v-model="constraints.employee.availability[employee.id][day.key].available"
                        size="small"
                      />
                      <div 
                        v-if="constraints.employee.availability[employee.id][day.key].available"
                        class="time-range"
                      >
                        <a-time-picker
                          v-model="constraints.employee.availability[employee.id][day.key].start"
                          format="HH:mm"
                          size="small"
                          placeholder="开始"
                        />
                        <a-time-picker
                          v-model="constraints.employee.availability[employee.id][day.key].end"
                          format="HH:mm"
                          size="small"
                          placeholder="结束"
                        />
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>

          <a-divider />

          <h5>员工偏好设置</h5>
          <div class="employee-preferences">
            <div 
              v-for="employee in employees" 
              :key="`pref-${employee.id}`"
              class="preference-item"
            >
              <div class="preference-header">
                <a-avatar :size="20" :style="{ backgroundColor: getAvatarColor(employee.name) }">
                  {{ employee.name.charAt(0) }}
                </a-avatar>
                <span class="employee-name">{{ employee.name }}</span>
              </div>
              
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="偏好班次" size="small">
                    <a-select 
                      mode="multiple"
                      v-model="constraints.employee.preferences[employee.id].preferredShifts"
                      size="small"
                      placeholder="选择偏好班次"
                    >
                      <a-select-option value="早班">早班</a-select-option>
                      <a-select-option value="晚班">晚班</a-select-option>
                      <a-select-option value="全天">全天</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="避免班次" size="small">
                    <a-select 
                      mode="multiple"
                      v-model="constraints.employee.preferences[employee.id].avoidShifts"
                      size="small"
                      placeholder="选择避免班次"
                    >
                      <a-select-option value="早班">早班</a-select-option>
                      <a-select-option value="晚班">晚班</a-select-option>
                      <a-select-option value="全天">全天</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="最大连续天数" size="small">
                    <a-input-number 
                      v-model="constraints.employee.preferences[employee.id].maxConsecutiveDays"
                      :min="1"
                      :max="7"
                      size="small"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-tab-pane>

      <!-- 时间约束 -->
      <a-tab-pane key="time" tab="时间约束">
        <div class="constraint-section">
          <h5>班次人员配置</h5>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card size="small" title="早班">
                <a-form-item label="最少人数">
                  <a-input-number 
                    v-model="constraints.time.minStaffPerShift.早班"
                    :min="0"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="最多人数">
                  <a-input-number 
                    v-model="constraints.time.maxStaffPerShift.早班"
                    :min="1"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" title="晚班">
                <a-form-item label="最少人数">
                  <a-input-number 
                    v-model="constraints.time.minStaffPerShift.晚班"
                    :min="0"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="最多人数">
                  <a-input-number 
                    v-model="constraints.time.maxStaffPerShift.晚班"
                    :min="1"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" title="全天">
                <a-form-item label="最少人数">
                  <a-input-number 
                    v-model="constraints.time.minStaffPerShift.全天"
                    :min="0"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="最多人数">
                  <a-input-number 
                    v-model="constraints.time.maxStaffPerShift.全天"
                    :min="1"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-card>
            </a-col>
          </a-row>

          <a-divider />

          <h5>覆盖要求</h5>
          <div class="coverage-config">
            <a-row :gutter="16">
              <a-col 
                v-for="day in weekDays" 
                :key="`coverage-${day.key}`"
                :span="3"
              >
                <a-card size="small" :title="day.label">
                  <a-checkbox 
                    v-model="constraints.time.requiredCoverage[day.key].required"
                  >
                    必须覆盖
                  </a-checkbox>
                  <div 
                    v-if="constraints.time.requiredCoverage[day.key].required"
                    class="coverage-time"
                  >
                    <a-time-picker
                      v-model="constraints.time.requiredCoverage[day.key].start"
                      format="HH:mm"
                      size="small"
                      placeholder="开始"
                    />
                    <a-time-picker
                      v-model="constraints.time.requiredCoverage[day.key].end"
                      format="HH:mm"
                      size="small"
                      placeholder="结束"
                    />
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-tab-pane>

      <!-- 业务约束 -->
      <a-tab-pane key="business" tab="业务约束">
        <div class="constraint-section">
          <h5>特殊事件</h5>
          <div class="special-events">
            <a-button 
              type="dashed" 
              icon="plus"
              @click="addSpecialEvent"
              style="width: 100%; margin-bottom: 16px"
            >
              添加特殊事件
            </a-button>
            
            <div 
              v-for="(event, index) in constraints.business.specialEvents" 
              :key="index"
              class="event-item"
            >
              <a-card size="small">
                <template #extra>
                  <a-button 
                    type="link" 
                    icon="delete"
                    @click="removeSpecialEvent(index)"
                  />
                </template>
                
                <a-row :gutter="16">
                  <a-col :span="6">
                    <a-form-item label="日期">
                      <a-date-picker 
                        v-model="event.date"
                        format="YYYY-MM-DD"
                        size="small"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="所需人数">
                      <a-input-number 
                        v-model="event.requiredStaff"
                        :min="1"
                        size="small"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="所需技能">
                      <a-select 
                        mode="multiple"
                        v-model="event.requiredSkills"
                        size="small"
                        placeholder="选择技能"
                      >
                        <a-select-option value="接待">接待</a-select-option>
                        <a-select-option value="制作">制作</a-select-option>
                        <a-select-option value="销售">销售</a-select-option>
                        <a-select-option value="管理">管理</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="事件描述">
                      <a-input 
                        v-model="event.description"
                        size="small"
                        placeholder="描述事件"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </div>
          </div>

          <a-divider />

          <h5>节假日安排</h5>
          <div class="holiday-config">
            <a-button 
              type="dashed" 
              icon="plus"
              @click="addHoliday"
              style="width: 100%; margin-bottom: 16px"
            >
              添加节假日
            </a-button>
            
            <div 
              v-for="(holiday, index) in constraints.business.holidays" 
              :key="`holiday-${index}`"
              class="holiday-item"
            >
              <a-card size="small">
                <template #extra>
                  <a-button 
                    type="link" 
                    icon="delete"
                    @click="removeHoliday(index)"
                  />
                </template>
                
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="日期">
                      <a-date-picker 
                        v-model="holiday.date"
                        format="YYYY-MM-DD"
                        size="small"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="类型">
                      <a-select 
                        v-model="holiday.type"
                        size="small"
                        style="width: 100%"
                      >
                        <a-select-option value="weekend">周末</a-select-option>
                        <a-select-option value="holiday">法定节假日</a-select-option>
                        <a-select-option value="special">特殊假日</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="最少人数">
                      <a-input-number 
                        v-model="holiday.minStaff"
                        :min="0"
                        size="small"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'ConstraintConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    employees: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      activeTab: 'employee',
      weekDays: [
        { key: 'monday', label: '周一' },
        { key: 'tuesday', label: '周二' },
        { key: 'wednesday', label: '周三' },
        { key: 'thursday', label: '周四' },
        { key: 'friday', label: '周五' },
        { key: 'saturday', label: '周六' },
        { key: 'sunday', label: '周日' }
      ]
    }
  },
  
  computed: {
    constraints: {
      get() {
        return this.mergeWithDefaults(this.value)
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  
  watch: {
    employees: {
      handler() {
        this.initializeEmployeeConstraints()
      },
      immediate: true
    }
  },
  
  methods: {
    // 合并默认值
    mergeWithDefaults(value) {
      const defaults = {
        employee: {
          availability: {},
          preferences: {}
        },
        time: {
          minStaffPerShift: {
            早班: 1,
            晚班: 1,
            全天: 1
          },
          maxStaffPerShift: {
            早班: 3,
            晚班: 3,
            全天: 2
          },
          requiredCoverage: {}
        },
        business: {
          specialEvents: [],
          holidays: []
        }
      }
      
      return this.deepMerge(defaults, value || {})
    },
    
    // 深度合并对象
    deepMerge(target, source) {
      const result = { ...target }
      
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(target[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
      
      return result
    },
    
    // 初始化员工约束
    initializeEmployeeConstraints() {
      const constraints = { ...this.constraints }
      
      this.employees.forEach(employee => {
        // 初始化可用时间
        if (!constraints.employee.availability[employee.id]) {
          constraints.employee.availability[employee.id] = {
            enabled: true
          }
          
          this.weekDays.forEach(day => {
            constraints.employee.availability[employee.id][day.key] = {
              available: true,
              start: moment('09:00', 'HH:mm'),
              end: moment('18:00', 'HH:mm')
            }
          })
        }
        
        // 初始化偏好设置
        if (!constraints.employee.preferences[employee.id]) {
          constraints.employee.preferences[employee.id] = {
            preferredShifts: [],
            avoidShifts: [],
            maxConsecutiveDays: 5
          }
        }
      })
      
      // 初始化时间覆盖要求
      this.weekDays.forEach(day => {
        if (!constraints.time.requiredCoverage[day.key]) {
          constraints.time.requiredCoverage[day.key] = {
            required: true,
            start: moment('09:00', 'HH:mm'),
            end: moment('18:00', 'HH:mm')
          }
        }
      })
      
      this.constraints = constraints
    },
    
    // 添加特殊事件
    addSpecialEvent() {
      this.constraints.business.specialEvents.push({
        date: moment(),
        requiredStaff: 2,
        requiredSkills: [],
        description: ''
      })
    },
    
    // 删除特殊事件
    removeSpecialEvent(index) {
      this.constraints.business.specialEvents.splice(index, 1)
    },
    
    // 添加节假日
    addHoliday() {
      this.constraints.business.holidays.push({
        date: moment(),
        type: 'weekend',
        minStaff: 1
      })
    },
    
    // 删除节假日
    removeHoliday(index) {
      this.constraints.business.holidays.splice(index, 1)
    },
    
    // 获取头像颜色
    getAvatarColor(name) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
      const index = name.charCodeAt(0) % colors.length
      return colors[index]
    }
  }
}
</script>

<style scoped>
.constraint-config {
  max-height: 400px;
  overflow-y: auto;
}

.constraint-section {
  padding: 8px 0;
}

.constraint-section h5 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 600;
}

.employee-item,
.preference-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.employee-header,
.preference-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.employee-name {
  font-weight: 500;
  flex: 1;
}

.availability-config {
  margin-top: 12px;
}

.day-config {
  text-align: center;
}

.day-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.time-range {
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.coverage-config {
  margin-top: 12px;
}

.coverage-time {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.event-item,
.holiday-item {
  margin-bottom: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .constraint-config {
    max-height: none;
  }
  
  .day-config {
    margin-bottom: 12px;
  }
}
</style>
