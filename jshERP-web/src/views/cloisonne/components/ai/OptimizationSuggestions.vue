<template>
  <div class="optimization-suggestions">
    <div class="suggestions-header">
      <h4>优化建议</h4>
      <p>基于AI分析的排班优化建议</p>
    </div>

    <div class="suggestions-list">
      <div 
        v-for="(suggestion, index) in suggestions" 
        :key="index"
        class="suggestion-item"
        :class="suggestion.type"
      >
        <div class="suggestion-icon">
          <a-icon :type="getSuggestionIcon(suggestion.type)" />
        </div>
        
        <div class="suggestion-content">
          <div class="suggestion-header">
            <h5 class="suggestion-title">{{ suggestion.title }}</h5>
            <a-tag 
              :color="getSuggestionColor(suggestion.type)"
              size="small"
            >
              {{ getSuggestionLabel(suggestion.type) }}
            </a-tag>
          </div>
          
          <p class="suggestion-description">{{ suggestion.description }}</p>
          
          <!-- 详细信息 -->
          <div v-if="suggestion.details" class="suggestion-details">
            <a-collapse size="small" :bordered="false">
              <a-collapse-panel key="details" header="查看详情">
                <div class="details-content">
                  <div v-if="suggestion.details.affected" class="affected-items">
                    <h6>影响范围：</h6>
                    <a-tag 
                      v-for="item in suggestion.details.affected" 
                      :key="item"
                      size="small"
                    >
                      {{ item }}
                    </a-tag>
                  </div>
                  
                  <div v-if="suggestion.details.metrics" class="metrics-info">
                    <h6>预期改善：</h6>
                    <a-row :gutter="8">
                      <a-col 
                        v-for="(value, metric) in suggestion.details.metrics" 
                        :key="metric"
                        :span="6"
                      >
                        <div class="metric-item">
                          <span class="metric-label">{{ getMetricLabel(metric) }}</span>
                          <span class="metric-value" :class="{ 'positive': value > 0, 'negative': value < 0 }">
                            {{ value > 0 ? '+' : '' }}{{ value }}%
                          </span>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                  
                  <div v-if="suggestion.details.steps" class="action-steps">
                    <h6>建议步骤：</h6>
                    <ol>
                      <li v-for="step in suggestion.details.steps" :key="step">
                        {{ step }}
                      </li>
                    </ol>
                  </div>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
          
          <!-- 操作按钮 -->
          <div class="suggestion-actions">
            <a-space>
              <a-button 
                v-if="suggestion.actionable"
                type="primary" 
                size="small"
                @click="applySuggestion(suggestion, index)"
              >
                应用建议
              </a-button>
              <a-button 
                size="small"
                @click="dismissSuggestion(index)"
              >
                忽略
              </a-button>
              <a-button 
                v-if="suggestion.type === 'warning' || suggestion.type === 'error'"
                size="small"
                @click="showSuggestionHelp(suggestion)"
              >
                获取帮助
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="suggestions.length === 0" class="empty-suggestions">
      <a-empty 
        description="暂无优化建议"
        :image="require('@/assets/images/empty.svg')"
      >
        <template #description>
          <span style="color: #999">
            当前排班方案已经很优秀了！
          </span>
        </template>
      </a-empty>
    </div>

    <!-- 建议统计 -->
    <div v-if="suggestions.length > 0" class="suggestions-summary">
      <a-divider />
      <div class="summary-content">
        <h5>建议统计</h5>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="总建议数" 
              :value="suggestions.length" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="高优先级" 
              :value="getCountByType('error')" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="中优先级" 
              :value="getCountByType('warning')" 
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="低优先级" 
              :value="getCountByType('info') + getCountByType('success')" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 帮助弹窗 -->
    <a-modal
      v-model="helpVisible"
      title="优化建议帮助"
      :footer="null"
      width="600px"
    >
      <div v-if="currentHelp" class="help-content">
        <h4>{{ currentHelp.title }}</h4>
        <p>{{ currentHelp.description }}</p>
        
        <div v-if="currentHelp.solutions" class="help-solutions">
          <h5>解决方案：</h5>
          <ul>
            <li v-for="solution in currentHelp.solutions" :key="solution">
              {{ solution }}
            </li>
          </ul>
        </div>
        
        <div v-if="currentHelp.examples" class="help-examples">
          <h5>示例：</h5>
          <div v-for="example in currentHelp.examples" :key="example.title" class="example-item">
            <h6>{{ example.title }}</h6>
            <p>{{ example.description }}</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'OptimizationSuggestions',
  props: {
    suggestions: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      helpVisible: false,
      currentHelp: null
    }
  },
  
  methods: {
    // 获取建议图标
    getSuggestionIcon(type) {
      const icons = {
        success: 'check-circle',
        info: 'info-circle',
        warning: 'exclamation-circle',
        error: 'close-circle'
      }
      return icons[type] || 'info-circle'
    },
    
    // 获取建议颜色
    getSuggestionColor(type) {
      const colors = {
        success: 'green',
        info: 'blue',
        warning: 'orange',
        error: 'red'
      }
      return colors[type] || 'blue'
    },
    
    // 获取建议标签
    getSuggestionLabel(type) {
      const labels = {
        success: '优秀',
        info: '提示',
        warning: '注意',
        error: '重要'
      }
      return labels[type] || '提示'
    },
    
    // 获取指标标签
    getMetricLabel(metric) {
      const labels = {
        coverage: '覆盖率',
        fairness: '公平性',
        satisfaction: '满意度',
        efficiency: '效率'
      }
      return labels[metric] || metric
    },
    
    // 按类型统计数量
    getCountByType(type) {
      return this.suggestions.filter(s => s.type === type).length
    },
    
    // 应用建议
    applySuggestion(suggestion, index) {
      this.$confirm({
        title: '确认应用建议',
        content: `确定要应用"${suggestion.title}"这个建议吗？`,
        onOk: () => {
          // 触发应用事件
          this.$emit('apply', {
            suggestion,
            index
          })
          
          // 显示成功消息
          this.$message.success('建议已应用，正在重新优化排班方案...')
        }
      })
    },
    
    // 忽略建议
    dismissSuggestion(index) {
      this.$emit('dismiss', index)
      this.$message.info('已忽略该建议')
    },
    
    // 显示建议帮助
    showSuggestionHelp(suggestion) {
      this.currentHelp = this.getHelpContent(suggestion)
      this.helpVisible = true
    },
    
    // 获取帮助内容
    getHelpContent(suggestion) {
      const helpMap = {
        'coverage_low': {
          title: '时段覆盖率不足',
          description: '某些时段没有足够的员工覆盖，可能影响正常运营。',
          solutions: [
            '增加该时段的员工排班',
            '调整其他时段的员工到此时段',
            '考虑招聘更多员工',
            '调整营业时间以匹配人员配置'
          ],
          examples: [
            {
              title: '午休时段覆盖不足',
              description: '可以安排轮班制，确保午休时段也有员工值班'
            }
          ]
        },
        'workload_unbalanced': {
          title: '工作量分配不均',
          description: '员工之间的工作量差异较大，可能影响团队和谐。',
          solutions: [
            '重新分配工作量较重员工的班次',
            '增加工作量较轻员工的排班',
            '考虑员工的个人能力和偏好',
            '建立轮班制度确保公平性'
          ],
          examples: [
            {
              title: '某员工连续多天值班',
              description: '可以安排其他员工分担，或给予适当的休息时间'
            }
          ]
        },
        'preference_conflict': {
          title: '员工偏好冲突',
          description: '当前排班与员工的时间偏好存在冲突。',
          solutions: [
            '与员工沟通协调时间安排',
            '考虑员工的个人情况和需求',
            '建立偏好轮换机制',
            '提供适当的补偿或激励'
          ],
          examples: [
            {
              title: '员工偏好早班但被安排晚班',
              description: '可以与其他员工协商调换，或在下次排班时优先考虑其偏好'
            }
          ]
        }
      }
      
      // 根据建议类型返回对应的帮助内容
      const key = suggestion.helpKey || 'default'
      return helpMap[key] || {
        title: suggestion.title,
        description: suggestion.description,
        solutions: ['请联系系统管理员获取更多帮助'],
        examples: []
      }
    }
  }
}
</script>

<style scoped>
.optimization-suggestions {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
}

.suggestions-header {
  margin-bottom: 20px;
}

.suggestions-header h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 600;
}

.suggestions-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestion-item.success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.suggestion-item.info {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.suggestion-item.warning {
  background: #fffbe6;
  border-color: #ffe58f;
}

.suggestion-item.error {
  background: #fff2f0;
  border-color: #ffccc7;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.suggestion-item.success .suggestion-icon {
  background: #52c41a;
  color: #fff;
}

.suggestion-item.info .suggestion-icon {
  background: #1890ff;
  color: #fff;
}

.suggestion-item.warning .suggestion-icon {
  background: #faad14;
  color: #fff;
}

.suggestion-item.error .suggestion-icon {
  background: #ff4d4f;
  color: #fff;
}

.suggestion-content {
  flex: 1;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-title {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.suggestion-description {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.suggestion-details {
  margin-bottom: 12px;
}

.details-content {
  padding: 8px 0;
}

.details-content h6 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.affected-items {
  margin-bottom: 12px;
}

.metrics-info {
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #fafafa;
  border-radius: 4px;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value {
  font-weight: 600;
  font-size: 12px;
}

.metric-value.positive {
  color: #52c41a;
}

.metric-value.negative {
  color: #ff4d4f;
}

.action-steps ol {
  margin: 0;
  padding-left: 20px;
}

.action-steps li {
  margin-bottom: 4px;
  color: #666;
  font-size: 14px;
}

.suggestion-actions {
  margin-top: 12px;
}

.empty-suggestions {
  text-align: center;
  padding: 40px 20px;
}

.suggestions-summary {
  margin-top: 20px;
}

.summary-content h5 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 600;
}

.help-content h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.help-content h5 {
  margin: 16px 0 8px 0;
  color: #333;
  font-weight: 600;
}

.help-content h6 {
  margin: 12px 0 4px 0;
  color: #333;
  font-weight: 600;
}

.help-solutions ul {
  margin: 0;
  padding-left: 20px;
}

.help-solutions li {
  margin-bottom: 4px;
  color: #666;
}

.example-item {
  margin-bottom: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.example-item h6 {
  margin: 0 0 4px 0;
}

.example-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .optimization-suggestions {
    padding: 16px;
  }
  
  .suggestion-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .suggestion-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .suggestion-actions {
    margin-top: 8px;
  }
}
</style>
