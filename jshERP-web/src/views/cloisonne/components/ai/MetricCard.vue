<template>
  <div class="metric-card" :class="cardClass">
    <div class="metric-icon">
      <a-icon :type="icon" />
    </div>
    <div class="metric-content">
      <div class="metric-value">
        <span class="value-number">{{ displayValue }}</span>
        <span v-if="suffix" class="value-suffix">{{ suffix }}</span>
      </div>
      <div class="metric-title">{{ title }}</div>
      <div v-if="trend" class="metric-trend">
        <a-icon :type="trend.direction === 'up' ? 'arrow-up' : 'arrow-down'" />
        <span>{{ trend.value }}{{ trend.suffix || '%' }}</span>
        <span class="trend-label">{{ trend.label || '较上期' }}</span>
      </div>
    </div>
    <div v-if="showProgress" class="metric-progress">
      <a-progress 
        :percent="progressPercent" 
        :show-info="false"
        :stroke-color="progressColor"
        size="small"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MetricCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    suffix: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'bar-chart'
    },
    color: {
      type: String,
      default: '#1890ff'
    },
    trend: {
      type: Object,
      default: null
      // { direction: 'up'|'down', value: number, suffix: string, label: string }
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    maxValue: {
      type: Number,
      default: 100
    },
    animated: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'default', // small, default, large
      validator: value => ['small', 'default', 'large'].includes(value)
    }
  },
  
  data() {
    return {
      animatedValue: 0,
      animationTimer: null
    }
  },
  
  computed: {
    displayValue() {
      if (this.animated && typeof this.value === 'number') {
        return this.animatedValue.toFixed(this.getDecimalPlaces())
      }
      return this.value
    },
    
    progressPercent() {
      if (typeof this.value !== 'number') return 0
      return Math.min((this.value / this.maxValue) * 100, 100)
    },
    
    progressColor() {
      if (this.value >= this.maxValue * 0.9) return '#52c41a'
      if (this.value >= this.maxValue * 0.7) return '#faad14'
      return '#ff4d4f'
    },
    
    cardClass() {
      return [
        `metric-card-${this.size}`,
        {
          'metric-card-excellent': this.isExcellent,
          'metric-card-good': this.isGood,
          'metric-card-warning': this.isWarning,
          'metric-card-danger': this.isDanger
        }
      ]
    },
    
    isExcellent() {
      return typeof this.value === 'number' && this.value >= 90
    },
    
    isGood() {
      return typeof this.value === 'number' && this.value >= 70 && this.value < 90
    },
    
    isWarning() {
      return typeof this.value === 'number' && this.value >= 50 && this.value < 70
    },
    
    isDanger() {
      return typeof this.value === 'number' && this.value < 50
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        if (this.animated && typeof newVal === 'number') {
          this.animateValue(newVal)
        }
      },
      immediate: true
    }
  },
  
  methods: {
    animateValue(targetValue) {
      if (this.animationTimer) {
        clearInterval(this.animationTimer)
      }
      
      const startValue = this.animatedValue
      const duration = 1000 // 1秒动画
      const startTime = Date.now()
      
      this.animationTimer = setInterval(() => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // 使用缓动函数
        const easeProgress = this.easeOutCubic(progress)
        this.animatedValue = startValue + (targetValue - startValue) * easeProgress
        
        if (progress >= 1) {
          clearInterval(this.animationTimer)
          this.animatedValue = targetValue
        }
      }, 16) // 60fps
    },
    
    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3)
    },
    
    getDecimalPlaces() {
      if (typeof this.value !== 'number') return 0
      if (this.value >= 100) return 0
      if (this.value >= 10) return 1
      return 2
    }
  },
  
  beforeDestroy() {
    if (this.animationTimer) {
      clearInterval(this.animationTimer)
    }
  }
}
</script>

<style scoped>
.metric-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transition: all 0.3s ease;
}

.metric-card-excellent::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.metric-card-good::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.metric-card-warning::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.metric-card-danger::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.metric-card-excellent .metric-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.metric-card-good .metric-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.metric-card-warning .metric-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.metric-card-danger .metric-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.metric-content {
  margin-bottom: 12px;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.value-suffix {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.metric-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.metric-trend .anticon {
  color: #52c41a;
}

.metric-trend .anticon[type="arrow-down"] {
  color: #ff4d4f;
}

.trend-label {
  color: #999;
  margin-left: 4px;
}

.metric-progress {
  margin-top: 12px;
}

/* 尺寸变体 */
.metric-card-small {
  padding: 16px;
}

.metric-card-small .metric-icon {
  width: 36px;
  height: 36px;
  font-size: 18px;
  margin-bottom: 12px;
}

.metric-card-small .value-number {
  font-size: 24px;
}

.metric-card-small .value-suffix {
  font-size: 14px;
}

.metric-card-small .metric-title {
  font-size: 12px;
}

.metric-card-large {
  padding: 24px;
}

.metric-card-large .metric-icon {
  width: 56px;
  height: 56px;
  font-size: 28px;
  margin-bottom: 20px;
}

.metric-card-large .value-number {
  font-size: 40px;
}

.metric-card-large .value-suffix {
  font-size: 18px;
}

.metric-card-large .metric-title {
  font-size: 16px;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.metric-card:hover .metric-icon {
  animation: pulse 0.6s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-card {
    padding: 16px;
  }
  
  .metric-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .value-number {
    font-size: 28px;
  }
  
  .value-suffix {
    font-size: 14px;
  }
  
  .metric-title {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .metric-card {
    padding: 12px;
  }
  
  .metric-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .value-number {
    font-size: 24px;
  }
  
  .metric-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}
</style>
