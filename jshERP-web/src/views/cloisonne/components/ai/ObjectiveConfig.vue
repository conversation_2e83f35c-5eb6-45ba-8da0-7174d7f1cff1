<template>
  <div class="objective-config">
    <div class="objective-header">
      <h5>优化目标权重配置</h5>
      <a-tooltip title="调整各项优化目标的重要程度，总权重为100%">
        <a-icon type="question-circle" />
      </a-tooltip>
    </div>

    <div class="objectives-list">
      <!-- 覆盖率 -->
      <div class="objective-item">
        <div class="objective-info">
          <div class="objective-icon coverage">
            <a-icon type="check-circle" />
          </div>
          <div class="objective-details">
            <h6>时段覆盖率</h6>
            <p>确保所有时段都有足够的人员覆盖</p>
          </div>
          <div class="objective-weight">
            <span class="weight-value">{{ objectives.coverage }}%</span>
          </div>
        </div>
        <div class="objective-slider">
          <a-slider 
            v-model="objectives.coverage"
            :min="0"
            :max="100"
            :step="5"
            @change="handleWeightChange"
          />
        </div>
        <div class="objective-description">
          <a-tag :color="getObjectiveColor('coverage')">
            {{ getObjectiveLevel(objectives.coverage) }}
          </a-tag>
          <span class="description-text">
            {{ getObjectiveDescription('coverage', objectives.coverage) }}
          </span>
        </div>
      </div>

      <!-- 公平性 -->
      <div class="objective-item">
        <div class="objective-info">
          <div class="objective-icon fairness">
            <a-icon type="team" />
          </div>
          <div class="objective-details">
            <h6>工作公平性</h6>
            <p>平衡各员工的工作量分配</p>
          </div>
          <div class="objective-weight">
            <span class="weight-value">{{ objectives.fairness }}%</span>
          </div>
        </div>
        <div class="objective-slider">
          <a-slider 
            v-model="objectives.fairness"
            :min="0"
            :max="100"
            :step="5"
            @change="handleWeightChange"
          />
        </div>
        <div class="objective-description">
          <a-tag :color="getObjectiveColor('fairness')">
            {{ getObjectiveLevel(objectives.fairness) }}
          </a-tag>
          <span class="description-text">
            {{ getObjectiveDescription('fairness', objectives.fairness) }}
          </span>
        </div>
      </div>

      <!-- 满意度 -->
      <div class="objective-item">
        <div class="objective-info">
          <div class="objective-icon satisfaction">
            <a-icon type="smile" />
          </div>
          <div class="objective-details">
            <h6>员工满意度</h6>
            <p>尽量满足员工的时间偏好</p>
          </div>
          <div class="objective-weight">
            <span class="weight-value">{{ objectives.satisfaction }}%</span>
          </div>
        </div>
        <div class="objective-slider">
          <a-slider 
            v-model="objectives.satisfaction"
            :min="0"
            :max="100"
            :step="5"
            @change="handleWeightChange"
          />
        </div>
        <div class="objective-description">
          <a-tag :color="getObjectiveColor('satisfaction')">
            {{ getObjectiveLevel(objectives.satisfaction) }}
          </a-tag>
          <span class="description-text">
            {{ getObjectiveDescription('satisfaction', objectives.satisfaction) }}
          </span>
        </div>
      </div>

      <!-- 成本效率 -->
      <div class="objective-item">
        <div class="objective-info">
          <div class="objective-icon cost">
            <a-icon type="dollar" />
          </div>
          <div class="objective-details">
            <h6>成本效率</h6>
            <p>优化人力成本，避免过度排班</p>
          </div>
          <div class="objective-weight">
            <span class="weight-value">{{ objectives.costEfficiency }}%</span>
          </div>
        </div>
        <div class="objective-slider">
          <a-slider 
            v-model="objectives.costEfficiency"
            :min="0"
            :max="100"
            :step="5"
            @change="handleWeightChange"
          />
        </div>
        <div class="objective-description">
          <a-tag :color="getObjectiveColor('costEfficiency')">
            {{ getObjectiveLevel(objectives.costEfficiency) }}
          </a-tag>
          <span class="description-text">
            {{ getObjectiveDescription('costEfficiency', objectives.costEfficiency) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 权重总计 -->
    <div class="weight-summary">
      <div class="summary-header">
        <span>权重总计</span>
        <span class="total-weight" :class="{ 'invalid': totalWeight !== 100 }">
          {{ totalWeight }}%
        </span>
      </div>
      <a-progress 
        :percent="totalWeight" 
        :status="totalWeight === 100 ? 'success' : 'exception'"
        :stroke-color="totalWeight === 100 ? '#52c41a' : '#ff4d4f'"
      />
      <div v-if="totalWeight !== 100" class="weight-warning">
        <a-icon type="exclamation-circle" />
        <span>权重总和应为100%，当前为{{ totalWeight }}%</span>
        <a-button 
          type="link" 
          size="small"
          @click="normalizeWeights"
        >
          自动调整
        </a-button>
      </div>
    </div>

    <!-- 预设方案 -->
    <div class="preset-schemes">
      <h6>预设方案</h6>
      <div class="scheme-buttons">
        <a-button 
          size="small"
          @click="applyPreset('balanced')"
        >
          均衡方案
        </a-button>
        <a-button 
          size="small"
          @click="applyPreset('coverage')"
        >
          覆盖优先
        </a-button>
        <a-button 
          size="small"
          @click="applyPreset('fairness')"
        >
          公平优先
        </a-button>
        <a-button 
          size="small"
          @click="applyPreset('satisfaction')"
        >
          满意度优先
        </a-button>
        <a-button 
          size="small"
          @click="applyPreset('cost')"
        >
          成本优先
        </a-button>
      </div>
    </div>

    <!-- 高级设置 -->
    <a-collapse size="small" style="margin-top: 16px">
      <a-collapse-panel key="advanced" header="高级设置">
        <div class="advanced-settings">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="优化迭代次数">
                <a-input-number 
                  v-model="advanced.maxIterations"
                  :min="100"
                  :max="10000"
                  :step="100"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收敛阈值">
                <a-input-number 
                  v-model="advanced.convergenceThreshold"
                  :min="0.001"
                  :max="0.1"
                  :step="0.001"
                  :precision="3"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="种群大小">
                <a-input-number 
                  v-model="advanced.populationSize"
                  :min="50"
                  :max="500"
                  :step="10"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="变异率">
                <a-input-number 
                  v-model="advanced.mutationRate"
                  :min="0.01"
                  :max="0.5"
                  :step="0.01"
                  :precision="2"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
export default {
  name: 'ObjectiveConfig',
  props: {
    value: {
      type: Object,
      default: () => ({
        coverage: 40,
        fairness: 30,
        satisfaction: 20,
        costEfficiency: 10
      })
    }
  },
  
  data() {
    return {
      advanced: {
        maxIterations: 1000,
        convergenceThreshold: 0.01,
        populationSize: 100,
        mutationRate: 0.1
      }
    }
  },
  
  computed: {
    objectives: {
      get() {
        return { ...this.value }
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    
    totalWeight() {
      return this.objectives.coverage + 
             this.objectives.fairness + 
             this.objectives.satisfaction + 
             this.objectives.costEfficiency
    }
  },
  
  methods: {
    // 处理权重变化
    handleWeightChange() {
      this.$emit('input', this.objectives)
    },
    
    // 获取目标颜色
    getObjectiveColor(type) {
      const colors = {
        coverage: 'blue',
        fairness: 'green',
        satisfaction: 'orange',
        costEfficiency: 'purple'
      }
      return colors[type] || 'default'
    },
    
    // 获取目标等级
    getObjectiveLevel(weight) {
      if (weight >= 40) return '高优先级'
      if (weight >= 20) return '中优先级'
      if (weight >= 10) return '低优先级'
      return '忽略'
    },
    
    // 获取目标描述
    getObjectiveDescription(type, weight) {
      const descriptions = {
        coverage: {
          high: '严格确保所有时段都有充足人员',
          medium: '尽量保证重要时段的人员覆盖',
          low: '基本满足人员覆盖需求',
          ignore: '不考虑覆盖率要求'
        },
        fairness: {
          high: '严格平衡所有员工的工作量',
          medium: '适度平衡员工工作分配',
          low: '基本考虑工作量公平性',
          ignore: '不考虑工作量平衡'
        },
        satisfaction: {
          high: '优先满足员工时间偏好',
          medium: '适度考虑员工偏好',
          low: '基本考虑员工意愿',
          ignore: '不考虑员工偏好'
        },
        costEfficiency: {
          high: '严格控制人力成本',
          medium: '适度优化成本效率',
          low: '基本考虑成本因素',
          ignore: '不考虑成本控制'
        }
      }
      
      let level = 'ignore'
      if (weight >= 40) level = 'high'
      else if (weight >= 20) level = 'medium'
      else if (weight >= 10) level = 'low'
      
      return descriptions[type][level]
    },
    
    // 标准化权重
    normalizeWeights() {
      const total = this.totalWeight
      if (total === 0) return
      
      const factor = 100 / total
      this.objectives = {
        coverage: Math.round(this.objectives.coverage * factor),
        fairness: Math.round(this.objectives.fairness * factor),
        satisfaction: Math.round(this.objectives.satisfaction * factor),
        costEfficiency: Math.round(this.objectives.costEfficiency * factor)
      }
      
      // 处理舍入误差
      const newTotal = this.objectives.coverage + this.objectives.fairness + 
                      this.objectives.satisfaction + this.objectives.costEfficiency
      if (newTotal !== 100) {
        this.objectives.coverage += (100 - newTotal)
      }
      
      this.handleWeightChange()
    },
    
    // 应用预设方案
    applyPreset(scheme) {
      const presets = {
        balanced: {
          coverage: 25,
          fairness: 25,
          satisfaction: 25,
          costEfficiency: 25
        },
        coverage: {
          coverage: 50,
          fairness: 20,
          satisfaction: 20,
          costEfficiency: 10
        },
        fairness: {
          coverage: 20,
          fairness: 50,
          satisfaction: 20,
          costEfficiency: 10
        },
        satisfaction: {
          coverage: 20,
          fairness: 20,
          satisfaction: 50,
          costEfficiency: 10
        },
        cost: {
          coverage: 20,
          fairness: 20,
          satisfaction: 10,
          costEfficiency: 50
        }
      }
      
      if (presets[scheme]) {
        this.objectives = { ...presets[scheme] }
        this.handleWeightChange()
        this.$message.success(`已应用${this.getPresetName(scheme)}方案`)
      }
    },
    
    // 获取预设方案名称
    getPresetName(scheme) {
      const names = {
        balanced: '均衡',
        coverage: '覆盖优先',
        fairness: '公平优先',
        satisfaction: '满意度优先',
        cost: '成本优先'
      }
      return names[scheme] || scheme
    }
  }
}
</script>

<style scoped>
.objective-config {
  padding: 8px 0;
}

.objective-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.objective-header h5 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.objective-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.objective-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.objective-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 18px;
}

.objective-icon.coverage {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.objective-icon.fairness {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.objective-icon.satisfaction {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.objective-icon.cost {
  background: linear-gradient(135deg, #722ed1, #b37feb);
}

.objective-details {
  flex: 1;
}

.objective-details h6 {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 600;
}

.objective-details p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.objective-weight {
  text-align: right;
}

.weight-value {
  font-size: 18px;
  font-weight: 700;
  color: #1890ff;
}

.objective-slider {
  margin: 8px 0;
}

.objective-description {
  display: flex;
  align-items: center;
  gap: 8px;
}

.description-text {
  font-size: 12px;
  color: #666;
}

.weight-summary {
  padding: 16px;
  background: #f6f8fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.total-weight {
  font-size: 18px;
  font-weight: 700;
  color: #52c41a;
}

.total-weight.invalid {
  color: #ff4d4f;
}

.weight-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #ff4d4f;
  font-size: 12px;
}

.preset-schemes h6 {
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.scheme-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.advanced-settings {
  padding-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .objective-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .objective-weight {
    text-align: left;
  }
  
  .scheme-buttons {
    flex-direction: column;
  }
  
  .scheme-buttons .ant-btn {
    width: 100%;
  }
}
</style>
