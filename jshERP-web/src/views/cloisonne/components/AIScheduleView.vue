<template>
  <div class="ai-schedule-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <a-icon type="robot" />
            AI智能排班
          </h1>
          <p class="page-subtitle">基于人工智能的自动排班优化系统</p>
        </div>
        <div class="action-section">
          <a-button 
            type="primary" 
            icon="thunderbolt"
            size="large"
            :loading="generating"
            @click="handleQuickGenerate"
          >
            快速生成
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="24">
        <!-- 左侧配置面板 -->
        <a-col :span="8">
          <div class="config-panel">
            <!-- 基础配置 -->
            <a-card title="基础配置" class="config-card">
              <template #extra>
                <a-tooltip title="配置排班的基本参数">
                  <a-icon type="question-circle" />
                </a-tooltip>
              </template>
              
              <a-form layout="vertical">
                <!-- 时间范围 -->
                <a-form-item label="排班时间范围">
                  <a-range-picker 
                    v-model="config.dateRange"
                    format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
                
                <!-- 参与员工 -->
                <a-form-item label="参与员工">
                  <a-select 
                    mode="multiple"
                    v-model="config.employees"
                    placeholder="选择参与排班的员工"
                    style="width: 100%"
                  >
                    <a-select-option 
                      v-for="emp in employees" 
                      :key="emp.id" 
                      :value="emp.id"
                    >
                      <a-avatar :size="20" :style="{ backgroundColor: getAvatarColor(emp.name) }">
                        {{ emp.name.charAt(0) }}
                      </a-avatar>
                      <span style="margin-left: 8px">{{ emp.name }}</span>
                    </a-select-option>
                  </a-select>
                </a-form-item>
                
                <!-- 算法选择 -->
                <a-form-item label="优化算法">
                  <a-select v-model="config.algorithm" style="width: 100%">
                    <a-select-option value="genetic">
                      <a-icon type="experiment" /> 遗传算法
                    </a-select-option>
                    <a-select-option value="simulated_annealing">
                      <a-icon type="fire" /> 模拟退火
                    </a-select-option>
                    <a-select-option value="particle_swarm">
                      <a-icon type="cluster" /> 粒子群优化
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-form>
            </a-card>

            <!-- 约束配置 -->
            <a-card title="约束条件" class="config-card">
              <template #extra>
                <a-tooltip title="设置排班的约束条件">
                  <a-icon type="question-circle" />
                </a-tooltip>
              </template>
              
              <constraint-config 
                v-model="config.constraints"
                :employees="selectedEmployees"
              />
            </a-card>

            <!-- 优化目标 -->
            <a-card title="优化目标" class="config-card">
              <template #extra>
                <a-tooltip title="设置优化目标的权重">
                  <a-icon type="question-circle" />
                </a-tooltip>
              </template>
              
              <objective-config 
                v-model="config.objectives"
              />
            </a-card>
          </div>
        </a-col>

        <!-- 右侧结果面板 -->
        <a-col :span="16">
          <div class="result-panel">
            <!-- 生成控制 -->
            <a-card title="AI排班生成" class="control-card">
              <template #extra>
                <a-space>
                  <a-button 
                    type="primary" 
                    icon="play-circle"
                    :loading="generating"
                    :disabled="!canGenerate"
                    @click="handleGenerate"
                  >
                    开始生成
                  </a-button>
                  <a-button 
                    icon="stop"
                    :disabled="!generating"
                    @click="handleStop"
                  >
                    停止生成
                  </a-button>
                  <a-button 
                    icon="redo"
                    :disabled="generating"
                    @click="handleReset"
                  >
                    重置配置
                  </a-button>
                </a-space>
              </template>

              <!-- 生成进度 -->
              <div v-if="generating" class="generation-progress">
                <div class="progress-header">
                  <h4>正在生成排班方案...</h4>
                  <span class="progress-text">{{ progressText }}</span>
                </div>
                <a-progress 
                  :percent="progress" 
                  :status="progressStatus"
                  :stroke-color="progressColor"
                />
                <div class="progress-details">
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <a-statistic title="当前代数" :value="currentGeneration" />
                    </a-col>
                    <a-col :span="8">
                      <a-statistic title="最优适应度" :value="bestFitness" :precision="2" />
                    </a-col>
                    <a-col :span="8">
                      <a-statistic title="已用时间" :value="elapsedTime" suffix="秒" />
                    </a-col>
                  </a-row>
                </div>
              </div>

              <!-- 配置预览 -->
              <div v-else class="config-preview">
                <a-descriptions title="当前配置" :column="2" size="small">
                  <a-descriptions-item label="时间范围">
                    {{ formatDateRange(config.dateRange) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="参与员工">
                    {{ config.employees.length }}人
                  </a-descriptions-item>
                  <a-descriptions-item label="优化算法">
                    {{ getAlgorithmName(config.algorithm) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="约束条件">
                    {{ getConstraintCount() }}项
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </a-card>

            <!-- 结果展示 -->
            <a-card 
              v-if="hasResult" 
              title="生成结果" 
              class="result-card"
            >
              <template #extra>
                <a-space>
                  <a-button 
                    type="primary" 
                    icon="check"
                    @click="handleApplyResult"
                  >
                    应用方案
                  </a-button>
                  <a-button 
                    icon="edit"
                    @click="handleEditResult"
                  >
                    手动调整
                  </a-button>
                  <a-button 
                    icon="download"
                    @click="handleExportResult"
                  >
                    导出结果
                  </a-button>
                </a-space>
              </template>

              <!-- 优化指标 -->
              <div class="metrics-section">
                <h4>优化指标</h4>
                <a-row :gutter="16">
                  <a-col :span="6">
                    <metric-card 
                      title="覆盖率"
                      :value="result.metrics.coverage"
                      suffix="%"
                      :color="getMetricColor(result.metrics.coverage)"
                      icon="check-circle"
                    />
                  </a-col>
                  <a-col :span="6">
                    <metric-card 
                      title="公平性"
                      :value="result.metrics.fairness"
                      suffix="%"
                      :color="getMetricColor(result.metrics.fairness)"
                      icon="team"
                    />
                  </a-col>
                  <a-col :span="6">
                    <metric-card 
                      title="满意度"
                      :value="result.metrics.satisfaction"
                      suffix="%"
                      :color="getMetricColor(result.metrics.satisfaction)"
                      icon="smile"
                    />
                  </a-col>
                  <a-col :span="6">
                    <metric-card 
                      title="成本效率"
                      :value="result.metrics.costEfficiency"
                      suffix="%"
                      :color="getMetricColor(result.metrics.costEfficiency)"
                      icon="dollar"
                    />
                  </a-col>
                </a-row>
              </div>

              <!-- 排班预览 -->
              <div class="schedule-preview">
                <h4>排班预览</h4>
                <schedule-preview-table 
                  :data="result.schedule"
                  :editable="true"
                  @change="handleScheduleChange"
                />
              </div>

              <!-- 优化建议 -->
              <div class="suggestions-section">
                <h4>优化建议</h4>
                <optimization-suggestions 
                  :suggestions="result.suggestions"
                  @apply="handleApplySuggestion"
                />
              </div>
            </a-card>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import ConstraintConfig from './ai/ConstraintConfig'
import ObjectiveConfig from './ai/ObjectiveConfig'
import MetricCard from './ai/MetricCard'
import SchedulePreviewTable from './ai/SchedulePreviewTable'
import OptimizationSuggestions from './ai/OptimizationSuggestions'
import ScheduleOptimizer from '../utils/ScheduleOptimizer'
import moment from 'moment'

export default {
  name: 'AIScheduleView',
  components: {
    ConstraintConfig,
    ObjectiveConfig,
    MetricCard,
    SchedulePreviewTable,
    OptimizationSuggestions
  },
  data() {
    return {
      // 配置参数
      config: {
        dateRange: [],
        employees: [],
        algorithm: 'genetic',
        constraints: {},
        objectives: {
          coverage: 40,
          fairness: 30,
          satisfaction: 20,
          costEfficiency: 10
        }
      },
      
      // 员工数据
      employees: [
        { id: 18, name: '聆花老师' },
        { id: 19, name: '梁朝伟' },
        { id: 20, name: '龚锦华' },
        { id: 21, name: '伍尚明' },
        { id: 22, name: '莫智华' }
      ],
      
      // 生成状态
      generating: false,
      progress: 0,
      progressText: '',
      progressStatus: 'active',
      currentGeneration: 0,
      bestFitness: 0,
      elapsedTime: 0,
      startTime: null,

      // 算法引擎
      optimizer: null,

      // 结果数据
      result: null,
      hasResult: false
    }
  },
  
  computed: {
    selectedEmployees() {
      return this.employees.filter(emp => 
        this.config.employees.includes(emp.id)
      )
    },
    
    canGenerate() {
      return this.config.dateRange.length === 2 && 
             this.config.employees.length > 0
    },
    
    progressColor() {
      if (this.progress < 30) return '#ff4d4f'
      if (this.progress < 70) return '#faad14'
      return '#52c41a'
    }
  },
  
  methods: {
    // 快速生成
    handleQuickGenerate() {
      // 使用默认配置快速生成
      this.setDefaultConfig()
      this.handleGenerate()
    },
    
    // 设置默认配置
    setDefaultConfig() {
      const today = new Date()
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      this.config.dateRange = [today, nextWeek]
      this.config.employees = this.employees.map(emp => emp.id)
    },
    
    // 开始生成
    async handleGenerate() {
      if (!this.validateConfig()) {
        return
      }

      this.generating = true
      this.progress = 0
      this.progressText = '初始化算法参数...'
      this.currentGeneration = 0
      this.bestFitness = 0
      this.elapsedTime = 0
      this.startTime = Date.now()
      this.progressStatus = 'active'

      try {
        // 创建优化器实例
        this.optimizer = new ScheduleOptimizer({
          populationSize: 100,
          maxGenerations: 500,
          mutationRate: 0.1,
          crossoverRate: 0.8
        })

        // 设置配置
        this.optimizer.setConfig({
          objectives: this.config.objectives,
          constraints: this.config.constraints,
          employees: this.selectedEmployees,
          shifts: this.getShiftDefinitions(),
          dateRange: this.getDateRange()
        })

        // 设置回调函数
        this.optimizer.onProgress = this.handleOptimizationProgress
        this.optimizer.onComplete = this.handleOptimizationComplete
        this.optimizer.onError = this.handleOptimizationError

        // 开始优化
        await this.optimizer.optimize()

      } catch (error) {
        this.handleOptimizationError(error)
      }
    },
    

    
    // 停止生成
    handleStop() {
      if (this.optimizer) {
        this.optimizer.stop()
      }
      this.generating = false
      this.progressStatus = 'exception'
      this.progressText = '已停止'
      this.$message.info('已停止生成')
    },

    // 优化进度回调
    handleOptimizationProgress(progressData) {
      this.currentGeneration = progressData.generation
      this.bestFitness = progressData.bestFitness
      this.progress = progressData.progress
      this.progressText = progressData.stage
      this.elapsedTime = Math.floor((Date.now() - this.startTime) / 1000)
    },

    // 优化完成回调
    handleOptimizationComplete(result) {
      this.result = result
      this.hasResult = true
      this.generating = false
      this.progressText = '生成完成'
      this.progress = 100
      this.progressStatus = 'success'
      this.$message.success('AI排班方案生成成功！')
    },

    // 优化错误回调
    handleOptimizationError(error) {
      this.generating = false
      this.progressStatus = 'exception'
      this.progressText = '生成失败'
      this.$message.error('生成失败：' + error.message)
    },
    
    // 重置配置
    handleReset() {
      this.config = {
        dateRange: [],
        employees: [],
        algorithm: 'genetic',
        constraints: {},
        objectives: {
          coverage: 40,
          fairness: 30,
          satisfaction: 20,
          costEfficiency: 10
        }
      }
      this.hasResult = false
      this.result = null
    },
    
    // 应用结果
    handleApplyResult() {
      this.$confirm({
        title: '确认应用',
        content: '确定要应用这个AI生成的排班方案吗？',
        onOk: () => {
          // 应用排班方案
          this.$message.success('排班方案已应用')
          this.$emit('apply-result', this.result)
        }
      })
    },
    
    // 编辑结果
    handleEditResult() {
      this.$message.info('进入手动调整模式')
      // 切换到编辑模式
    },
    
    // 导出结果
    handleExportResult() {
      this.$message.success('结果已导出')
    },
    
    // 工具方法
    disabledDate(current) {
      return current && current < new Date().setHours(0, 0, 0, 0)
    },
    
    getAvatarColor(name) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
      const index = name.charCodeAt(0) % colors.length
      return colors[index]
    },
    
    formatDateRange(range) {
      if (!range || range.length !== 2) return '未设置'
      return `${range[0].format('MM-DD')} 至 ${range[1].format('MM-DD')}`
    },
    
    getAlgorithmName(algorithm) {
      const names = {
        genetic: '遗传算法',
        simulated_annealing: '模拟退火',
        particle_swarm: '粒子群优化'
      }
      return names[algorithm] || algorithm
    },
    
    getConstraintCount() {
      return Object.keys(this.config.constraints).length
    },
    
    getMetricColor(value) {
      if (value >= 90) return '#52c41a'
      if (value >= 70) return '#faad14'
      return '#ff4d4f'
    },

    // 验证配置
    validateConfig() {
      if (!this.config.dateRange || this.config.dateRange.length !== 2) {
        this.$message.error('请选择有效的时间范围')
        return false
      }

      if (!this.config.employees || this.config.employees.length === 0) {
        this.$message.error('请至少选择一名员工')
        return false
      }

      // 验证权重总和
      const totalWeight = Object.values(this.config.objectives).reduce((sum, weight) => sum + weight, 0)
      if (Math.abs(totalWeight - 100) > 1) {
        this.$message.error('优化目标权重总和必须为100%')
        return false
      }

      return true
    },

    // 获取班次定义
    getShiftDefinitions() {
      return [
        {
          code: 'morning',
          name: '早班',
          startTime: '09:00',
          endTime: '13:00'
        },
        {
          code: 'evening',
          name: '晚班',
          startTime: '14:00',
          endTime: '18:00'
        },
        {
          code: 'fullday',
          name: '全天',
          startTime: '09:00',
          endTime: '18:00'
        }
      ]
    },

    // 获取日期范围
    getDateRange() {
      if (!this.config.dateRange || this.config.dateRange.length !== 2) {
        return []
      }

      const dates = []
      const start = this.config.dateRange[0].clone()
      const end = this.config.dateRange[1].clone()

      while (start.isSameOrBefore(end)) {
        dates.push(start.clone())
        start.add(1, 'day')
      }

      return dates
    }
  }
}
</script>

<style scoped>
.ai-schedule-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 16px;
}

.page-header {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  color: #1890ff;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 8px 0 0 0;
}

.main-content {
  gap: 24px;
}

.config-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-card,
.result-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.generation-progress {
  padding: 16px 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h4 {
  margin: 0;
  color: #333;
}

.progress-text {
  color: #666;
  font-size: 14px;
}

.progress-details {
  margin-top: 16px;
}

.config-preview {
  padding: 16px 0;
}

.metrics-section,
.schedule-preview,
.suggestions-section {
  margin-bottom: 24px;
}

.metrics-section h4,
.schedule-preview h4,
.suggestions-section h4 {
  margin-bottom: 16px;
  color: #333;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .ant-col:first-child {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .ai-schedule-container {
    padding: 8px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
}
</style>
