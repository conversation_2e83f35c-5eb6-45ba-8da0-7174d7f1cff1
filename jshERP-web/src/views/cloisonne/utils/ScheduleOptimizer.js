/**
 * AI智能排班优化器
 * 基于遗传算法的多目标优化排班系统
 */

class ScheduleOptimizer {
  constructor(config = {}) {
    // 算法参数
    this.populationSize = config.populationSize || 100
    this.maxGenerations = config.maxGenerations || 1000
    this.mutationRate = config.mutationRate || 0.1
    this.crossoverRate = config.crossoverRate || 0.8
    this.eliteSize = config.eliteSize || 10
    this.convergenceThreshold = config.convergenceThreshold || 0.01
    
    // 优化目标权重
    this.objectives = {
      coverage: 0.4,      // 覆盖率
      fairness: 0.3,      // 公平性
      satisfaction: 0.2,  // 满意度
      costEfficiency: 0.1 // 成本效率
    }
    
    // 约束条件
    this.constraints = {
      employee: {},
      time: {},
      business: {}
    }
    
    // 员工和班次数据
    this.employees = []
    this.shifts = []
    this.dateRange = []
    
    // 运行状态
    this.isRunning = false
    this.currentGeneration = 0
    this.bestFitness = 0
    this.population = []
    this.fitnessHistory = []
    
    // 回调函数
    this.onProgress = null
    this.onComplete = null
    this.onError = null
  }
  
  /**
   * 设置优化配置
   */
  setConfig(config) {
    if (config.objectives) {
      this.objectives = { ...this.objectives, ...config.objectives }
    }
    if (config.constraints) {
      this.constraints = { ...this.constraints, ...config.constraints }
    }
    if (config.employees) {
      this.employees = config.employees
    }
    if (config.shifts) {
      this.shifts = config.shifts
    }
    if (config.dateRange) {
      this.dateRange = config.dateRange
    }
  }
  
  /**
   * 开始优化
   */
  async optimize() {
    try {
      this.isRunning = true
      this.currentGeneration = 0
      this.bestFitness = 0
      this.fitnessHistory = []
      
      // 初始化种群
      await this.initializePopulation()
      
      // 进化循环
      for (let generation = 0; generation < this.maxGenerations; generation++) {
        this.currentGeneration = generation
        
        // 评估适应度
        await this.evaluatePopulation()
        
        // 检查收敛条件
        if (this.checkConvergence()) {
          break
        }
        
        // 选择、交叉、变异
        await this.evolvePopulation()
        
        // 报告进度
        if (this.onProgress) {
          this.onProgress({
            generation: this.currentGeneration,
            bestFitness: this.bestFitness,
            progress: (generation / this.maxGenerations) * 100,
            stage: this.getStageDescription(generation)
          })
        }
        
        // 让出控制权，避免阻塞UI
        await this.sleep(10)
        
        // 检查是否被停止
        if (!this.isRunning) {
          throw new Error('优化过程被用户停止')
        }
      }
      
      // 获取最优解
      const bestSolution = this.getBestSolution()
      const result = this.formatResult(bestSolution)
      
      if (this.onComplete) {
        this.onComplete(result)
      }
      
      return result
      
    } catch (error) {
      if (this.onError) {
        this.onError(error)
      }
      throw error
    } finally {
      this.isRunning = false
    }
  }
  
  /**
   * 停止优化
   */
  stop() {
    this.isRunning = false
  }
  
  /**
   * 初始化种群
   */
  async initializePopulation() {
    this.population = []
    
    for (let i = 0; i < this.populationSize; i++) {
      const individual = this.createRandomIndividual()
      this.population.push(individual)
    }
    
    if (this.onProgress) {
      this.onProgress({
        generation: 0,
        bestFitness: 0,
        progress: 5,
        stage: '初始化种群完成'
      })
    }
  }
  
  /**
   * 创建随机个体
   */
  createRandomIndividual() {
    const schedule = {}
    
    // 为每个日期和班次分配员工
    this.dateRange.forEach(date => {
      const dateKey = date.format('YYYY-MM-DD')
      schedule[dateKey] = {}
      
      this.shifts.forEach(shift => {
        schedule[dateKey][shift.code] = []
        
        // 随机分配员工
        const availableEmployees = this.getAvailableEmployees(date, shift)
        const minStaff = this.constraints.time.minStaffPerShift?.[shift.name] || 1
        const maxStaff = this.constraints.time.maxStaffPerShift?.[shift.name] || 3
        
        const staffCount = Math.floor(Math.random() * (maxStaff - minStaff + 1)) + minStaff
        
        for (let i = 0; i < Math.min(staffCount, availableEmployees.length); i++) {
          if (Math.random() > 0.3) { // 70%概率分配员工
            const randomEmployee = availableEmployees[Math.floor(Math.random() * availableEmployees.length)]
            if (!schedule[dateKey][shift.code].find(emp => emp.id === randomEmployee.id)) {
              schedule[dateKey][shift.code].push(randomEmployee)
            }
          }
        }
      })
    })
    
    return {
      schedule,
      fitness: 0,
      metrics: {}
    }
  }
  
  /**
   * 获取可用员工
   */
  getAvailableEmployees(date, shift) {
    return this.employees.filter(employee => {
      // 检查员工可用性约束
      const dayKey = date.format('dddd').toLowerCase()
      const availability = this.constraints.employee.availability?.[employee.id]?.[dayKey]
      
      if (availability && !availability.available) {
        return false
      }
      
      // 检查时间冲突
      if (availability && availability.start && availability.end) {
        const shiftStart = this.parseTime(shift.startTime)
        const shiftEnd = this.parseTime(shift.endTime)
        const availStart = this.parseTime(availability.start.format('HH:mm'))
        const availEnd = this.parseTime(availability.end.format('HH:mm'))
        
        if (shiftStart < availStart || shiftEnd > availEnd) {
          return false
        }
      }
      
      return true
    })
  }
  
  /**
   * 评估种群适应度
   */
  async evaluatePopulation() {
    for (let individual of this.population) {
      individual.fitness = this.calculateFitness(individual)
      individual.metrics = this.calculateMetrics(individual)
    }
    
    // 排序种群
    this.population.sort((a, b) => b.fitness - a.fitness)
    
    // 更新最佳适应度
    this.bestFitness = this.population[0].fitness
    this.fitnessHistory.push(this.bestFitness)
  }
  
  /**
   * 计算适应度
   */
  calculateFitness(individual) {
    const metrics = this.calculateMetrics(individual)
    
    // 加权求和
    const fitness = 
      metrics.coverage * this.objectives.coverage +
      metrics.fairness * this.objectives.fairness +
      metrics.satisfaction * this.objectives.satisfaction +
      metrics.costEfficiency * this.objectives.costEfficiency
    
    return fitness
  }
  
  /**
   * 计算各项指标
   */
  calculateMetrics(individual) {
    const coverage = this.calculateCoverage(individual.schedule)
    const fairness = this.calculateFairness(individual.schedule)
    const satisfaction = this.calculateSatisfaction(individual.schedule)
    const costEfficiency = this.calculateCostEfficiency(individual.schedule)
    
    return {
      coverage: coverage * 100,
      fairness: fairness * 100,
      satisfaction: satisfaction * 100,
      costEfficiency: costEfficiency * 100
    }
  }
  
  /**
   * 计算覆盖率
   */
  calculateCoverage(schedule) {
    let totalSlots = 0
    let coveredSlots = 0
    
    this.dateRange.forEach(date => {
      const dateKey = date.format('YYYY-MM-DD')
      this.shifts.forEach(shift => {
        totalSlots++
        const employees = schedule[dateKey]?.[shift.code] || []
        const minStaff = this.constraints.time.minStaffPerShift?.[shift.name] || 1
        
        if (employees.length >= minStaff) {
          coveredSlots++
        }
      })
    })
    
    return totalSlots > 0 ? coveredSlots / totalSlots : 0
  }
  
  /**
   * 计算公平性
   */
  calculateFairness(schedule) {
    const workCounts = {}
    
    // 统计每个员工的工作次数
    this.employees.forEach(emp => {
      workCounts[emp.id] = 0
    })
    
    Object.values(schedule).forEach(daySchedule => {
      Object.values(daySchedule).forEach(shiftEmployees => {
        shiftEmployees.forEach(emp => {
          workCounts[emp.id]++
        })
      })
    })
    
    // 计算标准差
    const counts = Object.values(workCounts)
    const mean = counts.reduce((sum, count) => sum + count, 0) / counts.length
    const variance = counts.reduce((sum, count) => sum + Math.pow(count - mean, 2), 0) / counts.length
    const stdDev = Math.sqrt(variance)
    
    // 公平性 = 1 - 标准化标准差
    const maxPossibleStdDev = mean // 最大可能的标准差
    return maxPossibleStdDev > 0 ? Math.max(0, 1 - stdDev / maxPossibleStdDev) : 1
  }
  
  /**
   * 计算满意度
   */
  calculateSatisfaction(schedule) {
    let totalPreferences = 0
    let satisfiedPreferences = 0
    
    Object.entries(schedule).forEach(([dateKey, daySchedule]) => {
      Object.entries(daySchedule).forEach(([shiftCode, employees]) => {
        employees.forEach(emp => {
          const preferences = this.constraints.employee.preferences?.[emp.id]
          if (preferences) {
            totalPreferences++
            
            const shift = this.shifts.find(s => s.code === shiftCode)
            if (shift && preferences.preferredShifts?.includes(shift.name)) {
              satisfiedPreferences++
            } else if (shift && !preferences.avoidShifts?.includes(shift.name)) {
              satisfiedPreferences += 0.5 // 中性偏好
            }
          }
        })
      })
    })
    
    return totalPreferences > 0 ? satisfiedPreferences / totalPreferences : 1
  }
  
  /**
   * 计算成本效率
   */
  calculateCostEfficiency(schedule) {
    let totalCost = 0
    let optimalCost = 0
    
    this.dateRange.forEach(date => {
      const dateKey = date.format('YYYY-MM-DD')
      this.shifts.forEach(shift => {
        const employees = schedule[dateKey]?.[shift.code] || []
        const minStaff = this.constraints.time.minStaffPerShift?.[shift.name] || 1
        
        totalCost += employees.length
        optimalCost += minStaff
      })
    })
    
    return totalCost > 0 ? optimalCost / totalCost : 1
  }
  
  /**
   * 检查收敛条件
   */
  checkConvergence() {
    if (this.fitnessHistory.length < 10) return false
    
    const recent = this.fitnessHistory.slice(-10)
    const improvement = recent[recent.length - 1] - recent[0]
    
    return improvement < this.convergenceThreshold
  }
  
  /**
   * 进化种群
   */
  async evolvePopulation() {
    const newPopulation = []
    
    // 保留精英
    for (let i = 0; i < this.eliteSize; i++) {
      newPopulation.push({ ...this.population[i] })
    }
    
    // 生成新个体
    while (newPopulation.length < this.populationSize) {
      const parent1 = this.selectParent()
      const parent2 = this.selectParent()
      
      let offspring
      if (Math.random() < this.crossoverRate) {
        offspring = this.crossover(parent1, parent2)
      } else {
        offspring = { ...parent1 }
      }
      
      if (Math.random() < this.mutationRate) {
        this.mutate(offspring)
      }
      
      newPopulation.push(offspring)
    }
    
    this.population = newPopulation
  }
  
  /**
   * 选择父代
   */
  selectParent() {
    // 锦标赛选择
    const tournamentSize = 3
    let best = this.population[Math.floor(Math.random() * this.population.length)]
    
    for (let i = 1; i < tournamentSize; i++) {
      const candidate = this.population[Math.floor(Math.random() * this.population.length)]
      if (candidate.fitness > best.fitness) {
        best = candidate
      }
    }
    
    return best
  }
  
  /**
   * 交叉操作
   */
  crossover(parent1, parent2) {
    const offspring = {
      schedule: {},
      fitness: 0,
      metrics: {}
    }
    
    // 单点交叉
    this.dateRange.forEach((date, index) => {
      const dateKey = date.format('YYYY-MM-DD')
      const useParent1 = index < this.dateRange.length / 2
      
      offspring.schedule[dateKey] = useParent1 ? 
        { ...parent1.schedule[dateKey] } : 
        { ...parent2.schedule[dateKey] }
    })
    
    return offspring
  }
  
  /**
   * 变异操作
   */
  mutate(individual) {
    // 随机选择一个日期和班次进行变异
    const randomDate = this.dateRange[Math.floor(Math.random() * this.dateRange.length)]
    const randomShift = this.shifts[Math.floor(Math.random() * this.shifts.length)]
    const dateKey = randomDate.format('YYYY-MM-DD')
    
    if (!individual.schedule[dateKey]) {
      individual.schedule[dateKey] = {}
    }
    if (!individual.schedule[dateKey][randomShift.code]) {
      individual.schedule[dateKey][randomShift.code] = []
    }
    
    const currentEmployees = individual.schedule[dateKey][randomShift.code]
    const availableEmployees = this.getAvailableEmployees(randomDate, randomShift)
    
    // 随机添加或移除员工
    if (Math.random() > 0.5 && availableEmployees.length > 0) {
      // 添加员工
      const randomEmployee = availableEmployees[Math.floor(Math.random() * availableEmployees.length)]
      if (!currentEmployees.find(emp => emp.id === randomEmployee.id)) {
        currentEmployees.push(randomEmployee)
      }
    } else if (currentEmployees.length > 0) {
      // 移除员工
      const randomIndex = Math.floor(Math.random() * currentEmployees.length)
      currentEmployees.splice(randomIndex, 1)
    }
  }
  
  /**
   * 获取最优解
   */
  getBestSolution() {
    return this.population[0]
  }
  
  /**
   * 格式化结果
   */
  formatResult(solution) {
    return {
      schedule: solution.schedule,
      metrics: solution.metrics,
      fitness: solution.fitness,
      generations: this.currentGeneration,
      suggestions: this.generateSuggestions(solution)
    }
  }
  
  /**
   * 生成优化建议
   */
  generateSuggestions(solution) {
    const suggestions = []
    
    if (solution.metrics.coverage < 80) {
      suggestions.push({
        type: 'warning',
        title: '覆盖率不足',
        description: '某些时段缺少足够的员工覆盖，建议增加人员安排',
        actionable: true
      })
    }
    
    if (solution.metrics.fairness < 70) {
      suggestions.push({
        type: 'warning',
        title: '工作量分配不均',
        description: '员工之间的工作量差异较大，建议重新平衡分配',
        actionable: true
      })
    }
    
    if (solution.metrics.satisfaction < 60) {
      suggestions.push({
        type: 'info',
        title: '员工偏好满足度较低',
        description: '当前排班与员工偏好存在较大差异，建议调整时间安排',
        actionable: true
      })
    }
    
    if (solution.metrics.coverage >= 90 && solution.metrics.fairness >= 80) {
      suggestions.push({
        type: 'success',
        title: '排班方案优秀',
        description: '当前方案在覆盖率和公平性方面表现出色',
        actionable: false
      })
    }
    
    return suggestions
  }
  
  /**
   * 工具方法
   */
  parseTime(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return hours * 60 + minutes
  }
  
  getStageDescription(generation) {
    const progress = generation / this.maxGenerations
    if (progress < 0.1) return '初始化种群...'
    if (progress < 0.3) return '计算适应度...'
    if (progress < 0.5) return '选择操作...'
    if (progress < 0.7) return '交叉操作...'
    if (progress < 0.9) return '变异操作...'
    return '优化解...'
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export default ScheduleOptimizer
