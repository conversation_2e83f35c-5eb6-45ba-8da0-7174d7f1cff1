/**
 * AI排班性能优化器
 * 提供算法性能优化和监控功能
 * 
 * <AUTHOR> Code
 * @since 2025-06-23
 */

class PerformanceOptimizer {
  constructor() {
    this.metrics = {
      executionTime: [],
      memoryUsage: [],
      convergenceRate: [],
      fitnessImprovement: []
    }
    
    this.optimizations = {
      adaptiveParameters: true,
      earlyTermination: true,
      parallelEvaluation: false,
      caching: true
    }
    
    this.cache = new Map()
    this.performanceThresholds = {
      maxExecutionTime: 30000,  // 30秒
      maxMemoryUsage: 100,      // 100MB
      minConvergenceRate: 0.01, // 1%
      targetFitness: 0.9        // 90%
    }
  }

  /**
   * 优化算法参数
   */
  optimizeParameters(config, historicalData = []) {
    const optimizedConfig = { ...config }
    
    if (this.optimizations.adaptiveParameters) {
      // 基于历史数据自适应调整参数
      if (historicalData.length > 0) {
        optimizedConfig.populationSize = this.adaptPopulationSize(historicalData)
        optimizedConfig.mutationRate = this.adaptMutationRate(historicalData)
        optimizedConfig.crossoverRate = this.adaptCrossoverRate(historicalData)
      }
      
      // 基于问题规模调整参数
      const problemSize = this.calculateProblemSize(config)
      optimizedConfig.maxGenerations = this.adaptMaxGenerations(problemSize)
    }
    
    return optimizedConfig
  }

  /**
   * 计算问题规模
   */
  calculateProblemSize(config) {
    const employees = config.employees?.length || 0
    const days = config.dateRange?.length || 0
    const shifts = config.shifts?.length || 0
    
    return employees * days * shifts
  }

  /**
   * 自适应种群大小
   */
  adaptPopulationSize(historicalData) {
    const avgConvergenceRate = this.calculateAverageConvergenceRate(historicalData)
    
    if (avgConvergenceRate < 0.01) {
      // 收敛太慢，增加种群大小
      return Math.min(200, Math.floor(100 * 1.5))
    } else if (avgConvergenceRate > 0.1) {
      // 收敛太快，可能陷入局部最优，减少种群大小
      return Math.max(50, Math.floor(100 * 0.8))
    }
    
    return 100 // 默认值
  }

  /**
   * 自适应变异率
   */
  adaptMutationRate(historicalData) {
    const avgFitnessImprovement = this.calculateAverageFitnessImprovement(historicalData)
    
    if (avgFitnessImprovement < 0.01) {
      // 改善太慢，增加变异率
      return Math.min(0.3, 0.1 * 1.5)
    } else if (avgFitnessImprovement > 0.1) {
      // 改善太快，可能破坏好解，减少变异率
      return Math.max(0.05, 0.1 * 0.7)
    }
    
    return 0.1 // 默认值
  }

  /**
   * 自适应交叉率
   */
  adaptCrossoverRate(historicalData) {
    const diversity = this.calculatePopulationDiversity(historicalData)
    
    if (diversity < 0.3) {
      // 多样性不足，增加交叉率
      return Math.min(0.95, 0.8 * 1.2)
    } else if (diversity > 0.8) {
      // 多样性过高，减少交叉率
      return Math.max(0.6, 0.8 * 0.8)
    }
    
    return 0.8 // 默认值
  }

  /**
   * 自适应最大代数
   */
  adaptMaxGenerations(problemSize) {
    // 根据问题规模调整最大代数
    if (problemSize < 100) {
      return 200
    } else if (problemSize < 500) {
      return 500
    } else if (problemSize < 1000) {
      return 800
    } else {
      return 1000
    }
  }

  /**
   * 性能监控
   */
  startPerformanceMonitoring() {
    this.startTime = performance.now()
    this.initialMemory = this.getMemoryUsage()
    
    // 定期收集性能指标
    this.monitoringInterval = setInterval(() => {
      this.collectPerformanceMetrics()
    }, 1000)
  }

  /**
   * 停止性能监控
   */
  stopPerformanceMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
    }
    
    const endTime = performance.now()
    const totalTime = endTime - this.startTime
    
    this.metrics.executionTime.push(totalTime)
    
    return {
      executionTime: totalTime,
      memoryUsage: this.getMemoryUsage() - this.initialMemory,
      averageConvergenceRate: this.calculateAverageConvergenceRate(this.metrics.convergenceRate),
      peakMemoryUsage: Math.max(...this.metrics.memoryUsage)
    }
  }

  /**
   * 收集性能指标
   */
  collectPerformanceMetrics() {
    const currentMemory = this.getMemoryUsage()
    this.metrics.memoryUsage.push(currentMemory)
    
    // 检查性能阈值
    this.checkPerformanceThresholds()
  }

  /**
   * 检查性能阈值
   */
  checkPerformanceThresholds() {
    const currentTime = performance.now() - this.startTime
    const currentMemory = this.getMemoryUsage()
    
    if (currentTime > this.performanceThresholds.maxExecutionTime) {
      console.warn('⚠️ 执行时间超过阈值')
    }
    
    if (currentMemory > this.performanceThresholds.maxMemoryUsage) {
      console.warn('⚠️ 内存使用超过阈值')
    }
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  /**
   * 缓存管理
   */
  getCachedResult(key) {
    if (this.optimizations.caching && this.cache.has(key)) {
      return this.cache.get(key)
    }
    return null
  }

  setCachedResult(key, result) {
    if (this.optimizations.caching) {
      // 限制缓存大小
      if (this.cache.size > 1000) {
        const firstKey = this.cache.keys().next().value
        this.cache.delete(firstKey)
      }
      
      this.cache.set(key, result)
    }
  }

  /**
   * 早期终止检查
   */
  shouldTerminateEarly(fitnessHistory, generation) {
    if (!this.optimizations.earlyTermination) {
      return false
    }
    
    // 检查是否达到目标适应度
    const currentFitness = fitnessHistory[fitnessHistory.length - 1]
    if (currentFitness >= this.performanceThresholds.targetFitness) {
      return true
    }
    
    // 检查是否停滞不前
    if (fitnessHistory.length >= 50) {
      const recent = fitnessHistory.slice(-50)
      const improvement = recent[recent.length - 1] - recent[0]
      
      if (improvement < this.performanceThresholds.minConvergenceRate) {
        return true
      }
    }
    
    return false
  }

  /**
   * 并行评估支持
   */
  async evaluatePopulationParallel(population, evaluationFunction) {
    if (!this.optimizations.parallelEvaluation) {
      // 串行评估
      for (let individual of population) {
        individual.fitness = evaluationFunction(individual)
      }
      return
    }
    
    // 并行评估（使用Web Workers）
    const chunkSize = Math.ceil(population.length / navigator.hardwareConcurrency)
    const chunks = this.chunkArray(population, chunkSize)
    
    const promises = chunks.map(chunk => 
      this.evaluateChunkParallel(chunk, evaluationFunction)
    )
    
    await Promise.all(promises)
  }

  /**
   * 分块数组
   */
  chunkArray(array, chunkSize) {
    const chunks = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * 并行评估分块
   */
  async evaluateChunkParallel(chunk, evaluationFunction) {
    // 这里可以使用Web Workers进行并行计算
    // 简化实现，直接串行处理
    for (let individual of chunk) {
      individual.fitness = evaluationFunction(individual)
    }
  }

  /**
   * 计算平均收敛率
   */
  calculateAverageConvergenceRate(data) {
    if (data.length === 0) return 0
    return data.reduce((sum, rate) => sum + rate, 0) / data.length
  }

  /**
   * 计算平均适应度改善
   */
  calculateAverageFitnessImprovement(data) {
    if (data.length === 0) return 0
    return data.reduce((sum, improvement) => sum + improvement, 0) / data.length
  }

  /**
   * 计算种群多样性
   */
  calculatePopulationDiversity(data) {
    // 简化实现，返回模拟值
    return Math.random() * 0.5 + 0.3
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    const report = {
      executionTime: {
        average: this.calculateAverage(this.metrics.executionTime),
        min: Math.min(...this.metrics.executionTime),
        max: Math.max(...this.metrics.executionTime)
      },
      memoryUsage: {
        average: this.calculateAverage(this.metrics.memoryUsage),
        peak: Math.max(...this.metrics.memoryUsage)
      },
      convergenceRate: {
        average: this.calculateAverage(this.metrics.convergenceRate)
      },
      recommendations: this.generateOptimizationRecommendations()
    }
    
    return report
  }

  /**
   * 生成优化建议
   */
  generateOptimizationRecommendations() {
    const recommendations = []
    
    const avgExecutionTime = this.calculateAverage(this.metrics.executionTime)
    if (avgExecutionTime > this.performanceThresholds.maxExecutionTime) {
      recommendations.push({
        type: 'performance',
        title: '执行时间过长',
        description: '建议减少种群大小或最大代数',
        priority: 'high'
      })
    }
    
    const peakMemory = Math.max(...this.metrics.memoryUsage)
    if (peakMemory > this.performanceThresholds.maxMemoryUsage) {
      recommendations.push({
        type: 'memory',
        title: '内存使用过高',
        description: '建议启用缓存清理或减少种群大小',
        priority: 'medium'
      })
    }
    
    const avgConvergenceRate = this.calculateAverage(this.metrics.convergenceRate)
    if (avgConvergenceRate < this.performanceThresholds.minConvergenceRate) {
      recommendations.push({
        type: 'convergence',
        title: '收敛速度慢',
        description: '建议调整变异率或增加种群多样性',
        priority: 'medium'
      })
    }
    
    return recommendations
  }

  /**
   * 计算平均值
   */
  calculateAverage(array) {
    if (array.length === 0) return 0
    return array.reduce((sum, value) => sum + value, 0) / array.length
  }

  /**
   * 重置性能指标
   */
  resetMetrics() {
    this.metrics = {
      executionTime: [],
      memoryUsage: [],
      convergenceRate: [],
      fitnessImprovement: []
    }
    this.cache.clear()
  }

  /**
   * 设置优化选项
   */
  setOptimizations(options) {
    this.optimizations = { ...this.optimizations, ...options }
  }

  /**
   * 设置性能阈值
   */
  setPerformanceThresholds(thresholds) {
    this.performanceThresholds = { ...this.performanceThresholds, ...thresholds }
  }
}

export default PerformanceOptimizer
