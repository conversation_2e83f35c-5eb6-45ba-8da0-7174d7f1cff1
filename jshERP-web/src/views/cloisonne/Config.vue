<template>
  <div class="cloisonne-config">
    <a-page-header
      title="模块配置"
      sub-title="掐丝珐琅馆系统配置管理"
      @back="() => $router.go(-1)"
    />

    <div class="content">
      <a-tabs default-active-key="general">
        <!-- 基础配置 -->
        <a-tab-pane key="general" tab="基础配置">
          <a-card title="店铺信息" style="margin-bottom: 16px;">
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="店铺名称">
                    <a-input v-model="config.shopName" placeholder="请输入店铺名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="联系电话">
                    <a-input v-model="config.phone" placeholder="请输入联系电话" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="店铺地址">
                    <a-input v-model="config.address" placeholder="请输入店铺地址" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="营业时间">
                    <a-time-picker.RangePicker
                      v-model="config.businessHours"
                      format="HH:mm"
                      style="width: 100%;"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>

          <a-card title="系统设置">
            <a-form layout="vertical">
              <a-form-item label="默认语言">
                <a-select v-model="config.language" style="width: 200px;">
                  <a-select-option value="zh-CN">简体中文</a-select-option>
                  <a-select-option value="en-US">English</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="时区">
                <a-select v-model="config.timezone" style="width: 200px;">
                  <a-select-option value="Asia/Shanghai">北京时间</a-select-option>
                  <a-select-option value="UTC">UTC</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="config.enableNotifications">启用系统通知</a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="config.enableAutoBackup">启用自动备份</a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>

        <!-- 支付配置 -->
        <a-tab-pane key="payment" tab="支付配置">
          <a-card title="支付方式">
            <a-form layout="vertical">
              <a-form-item>
                <a-checkbox v-model="paymentConfig.enableCash">现金支付</a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="paymentConfig.enableCard">银行卡支付</a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="paymentConfig.enableWechat">微信支付</a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="paymentConfig.enableAlipay">支付宝支付</a-checkbox>
              </a-form-item>
              <a-form-item label="税率设置">
                <a-input-number
                  v-model="paymentConfig.taxRate"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  :formatter="value => `${(value * 100).toFixed(2)}%`"
                  :parser="value => value.replace('%', '') / 100"
                  style="width: 200px;"
                />
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>

        <!-- 库存配置 -->
        <a-tab-pane key="inventory" tab="库存配置">
          <a-card title="库存预警">
            <a-form layout="vertical">
              <a-form-item label="低库存预警阈值">
                <a-input-number
                  v-model="inventoryConfig.lowStockThreshold"
                  :min="0"
                  style="width: 200px;"
                />
              </a-form-item>
              <a-form-item label="缺货预警阈值">
                <a-input-number
                  v-model="inventoryConfig.outOfStockThreshold"
                  :min="0"
                  style="width: 200px;"
                />
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="inventoryConfig.enableAutoReorder">启用自动补货</a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-checkbox v-model="inventoryConfig.enableStockAlerts">启用库存预警通知</a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>

        <!-- 员工配置 -->
        <a-tab-pane key="staff" tab="员工配置">
          <a-card title="排班设置">
            <a-form layout="vertical">
              <a-form-item label="工作日设置">
                <a-checkbox-group v-model="staffConfig.workDays">
                  <a-checkbox value="1">周一</a-checkbox>
                  <a-checkbox value="2">周二</a-checkbox>
                  <a-checkbox value="3">周三</a-checkbox>
                  <a-checkbox value="4">周四</a-checkbox>
                  <a-checkbox value="5">周五</a-checkbox>
                  <a-checkbox value="6">周六</a-checkbox>
                  <a-checkbox value="0">周日</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="班次设置">
                <a-table
                  :columns="shiftColumns"
                  :data-source="staffConfig.shifts"
                  :pagination="false"
                  size="small"
                >
                  <template slot="action" slot-scope="text, record, index">
                    <a-button size="small" @click="editShift(index)">编辑</a-button>
                    <a-button size="small" type="danger" @click="deleteShift(index)" style="margin-left: 8px;">删除</a-button>
                  </template>
                </a-table>
                <a-button type="dashed" @click="addShift" style="width: 100%; margin-top: 16px;">
                  <a-icon type="plus" />
                  添加班次
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>
      </a-tabs>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" size="large" @click="saveConfig" :loading="saving">
          保存配置
        </a-button>
        <a-button size="large" @click="resetConfig" style="margin-left: 16px;">
          重置
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CloisonneConfig',
  data() {
    return {
      saving: false,
      config: {
        shopName: '聆花珐琅馆',
        phone: '020-12345678',
        address: '广州市天河区珠江新城',
        businessHours: null,
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        enableNotifications: true,
        enableAutoBackup: true
      },
      paymentConfig: {
        enableCash: true,
        enableCard: true,
        enableWechat: true,
        enableAlipay: true,
        taxRate: 0.06
      },
      inventoryConfig: {
        lowStockThreshold: 10,
        outOfStockThreshold: 0,
        enableAutoReorder: false,
        enableStockAlerts: true
      },
      staffConfig: {
        workDays: ['1', '2', '3', '4', '5', '6'],
        shifts: [
          { id: 1, name: '早班', startTime: '09:00', endTime: '17:00' },
          { id: 2, name: '晚班', startTime: '17:00', endTime: '21:00' }
        ]
      },
      shiftColumns: [
        { title: '班次名称', dataIndex: 'name', key: 'name' },
        { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
        { title: '结束时间', dataIndex: 'endTime', key: 'endTime' },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' } }
      ]
    }
  },
  methods: {
    saveConfig() {
      this.saving = true
      // 模拟保存配置
      setTimeout(() => {
        this.saving = false
        this.$message.success('配置保存成功')
      }, 1000)
    },
    resetConfig() {
      this.$confirm({
        title: '确认重置',
        content: '确定要重置所有配置吗？此操作不可撤销。',
        onOk: () => {
          // 重置配置逻辑
          this.$message.success('配置已重置')
        }
      })
    },
    addShift() {
      this.$message.info('添加班次功能开发中...')
    },
    editShift(index) {
      this.$message.info(`编辑班次: ${this.staffConfig.shifts[index].name}`)
    },
    deleteShift(index) {
      this.staffConfig.shifts.splice(index, 1)
      this.$message.success('班次已删除')
    }
  }
}
</script>

<style lang="less" scoped>
.cloisonne-config {
  .content {
    padding: 24px;
  }

  .save-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
  }
}
</style>
