#!/bin/bash

# jshERP前端构建脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 构建符合官方部署标准的前端dist包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/jshERP-web"
BUILD_OUTPUT_DIR="$PROJECT_ROOT/dist/frontend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查构建环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 14.x+"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 14 ]; then
        log_error "Node.js版本过低，需要14.x+，当前版本: $(node --version)"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    fi
    
    # 检查前端目录
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_error "前端目录不存在: $FRONTEND_DIR"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 清理旧的构建文件
clean_build() {
    log_info "清理旧的构建文件..."
    
    # 清理前端dist目录
    if [ -d "$FRONTEND_DIR/dist" ]; then
        rm -rf "$FRONTEND_DIR/dist"
        log_info "已清理前端dist目录"
    fi
    
    # 清理输出目录
    if [ -d "$BUILD_OUTPUT_DIR" ]; then
        rm -rf "$BUILD_OUTPUT_DIR"
        log_info "已清理输出目录"
    fi
    
    # 创建输出目录
    mkdir -p "$BUILD_OUTPUT_DIR"
    
    log_success "清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."
    
    cd "$FRONTEND_DIR"
    
    # 检查package.json
    if [ ! -f "package.json" ]; then
        log_error "package.json文件不存在"
        exit 1
    fi
    
    # 清理node_modules（可选）
    if [ "$1" = "--clean" ]; then
        log_info "清理node_modules..."
        rm -rf node_modules package-lock.json
    fi
    
    # 安装依赖
    log_info "执行npm install..."
    npm install --legacy-peer-deps
    
    log_success "依赖安装完成"
}

# 创建生产环境配置
create_production_config() {
    log_info "创建生产环境配置..."
    
    cd "$FRONTEND_DIR"
    
    # 创建.env.production文件
    cat > .env.production << EOF
# jshERP生产环境配置
NODE_ENV=production
VUE_APP_API_BASE_URL=/jshERP-boot
VUE_APP_TITLE=管伊佳ERP
VUE_APP_VERSION=3.5.0
VUE_APP_BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
EOF
    
    log_success "生产环境配置创建完成"
}

# 构建前端项目
build_frontend() {
    log_info "构建前端项目..."
    
    cd "$FRONTEND_DIR"
    
    # 设置环境变量
    export NODE_ENV=production
    
    # 执行构建
    log_info "执行npm run build..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist目录不存在"
        exit 1
    fi
    
    # 检查关键文件
    if [ ! -f "dist/index.html" ]; then
        log_error "构建失败，index.html不存在"
        exit 1
    fi
    
    log_success "前端构建完成"
}

# 优化构建产物
optimize_build() {
    log_info "优化构建产物..."
    
    cd "$FRONTEND_DIR/dist"
    
    # 检查文件大小
    local total_size=$(du -sh . | cut -f1)
    log_info "构建产物总大小: $total_size"
    
    # 检查gzip压缩文件
    local gzip_files=$(find . -name "*.gz" | wc -l)
    if [ "$gzip_files" -gt 0 ]; then
        log_info "发现 $gzip_files 个gzip压缩文件"
    fi
    
    log_success "构建产物优化完成"
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    cd "$FRONTEND_DIR"
    
    # 复制dist到输出目录
    cp -r dist/* "$BUILD_OUTPUT_DIR/"
    
    # 创建压缩包
    cd "$BUILD_OUTPUT_DIR"
    zip -r "../dist.zip" .
    
    # 验证压缩包
    if [ ! -f "../dist.zip" ]; then
        log_error "压缩包创建失败"
        exit 1
    fi
    
    local zip_size=$(du -sh "../dist.zip" | cut -f1)
    log_info "部署包大小: $zip_size"
    
    log_success "部署包创建完成: $PROJECT_ROOT/dist/dist.zip"
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    local dist_zip="$PROJECT_ROOT/dist/dist.zip"
    
    # 检查压缩包
    if [ ! -f "$dist_zip" ]; then
        log_error "部署包不存在"
        exit 1
    fi
    
    # 检查压缩包内容
    local file_count=$(unzip -l "$dist_zip" | grep -c "\.html\|\.js\|\.css")
    if [ "$file_count" -lt 3 ]; then
        log_error "部署包内容不完整"
        exit 1
    fi
    
    log_success "构建验证通过"
}

# 主函数
main() {
    echo "=================================================="
    echo "    jshERP前端构建脚本"
    echo "=================================================="
    echo ""
    
    # 解析参数
    local clean_deps=false
    for arg in "$@"; do
        case $arg in
            --clean)
                clean_deps=true
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --clean    清理node_modules后重新安装"
                echo "  --help     显示此帮助信息"
                exit 0
                ;;
        esac
    done
    
    # 执行构建流程
    check_environment
    clean_build
    
    if [ "$clean_deps" = true ]; then
        install_dependencies --clean
    else
        install_dependencies
    fi
    
    create_production_config
    build_frontend
    optimize_build
    create_deployment_package
    verify_build
    
    echo ""
    echo "=================================================="
    log_success "前端构建完成！"
    echo ""
    echo "构建产物位置:"
    echo "- 部署包: $PROJECT_ROOT/dist/dist.zip"
    echo "- 源文件: $BUILD_OUTPUT_DIR"
    echo ""
    echo "下一步:"
    echo "1. 将dist.zip上传到服务器"
    echo "2. 解压到/home/<USER>/jshERP-web目录"
    echo "3. 配置Nginx代理"
    echo "=================================================="
}

# 执行主函数
main "$@"
