#!/bin/bash

# jshERP质量检查脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 全面检查jshERP标准化部署方案的质量和完整性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 统计变量
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNING_CHECKS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

log_check() {
    echo -e "${CYAN}[CHECK]${NC} $1"
    ((TOTAL_CHECKS++))
}

# 检查脚本文件
check_scripts() {
    log_info "检查构建脚本..."
    
    local scripts=(
        "scripts/build-frontend.sh"
        "scripts/build-backend.sh"
        "scripts/build-all.sh"
        "scripts/generate-config.sh"
        "scripts/prepare-database.sh"
        "scripts/monitor.sh"
        "scripts/fix-button-permissions.sh"
    )
    
    for script in "${scripts[@]}"; do
        log_check "检查脚本: $script"
        
        if [ -f "$PROJECT_ROOT/$script" ]; then
            if [ -x "$PROJECT_ROOT/$script" ]; then
                log_success "$script 存在且可执行"
            else
                log_warning "$script 存在但不可执行"
            fi
        else
            log_error "$script 不存在"
        fi
    done
}

# 检查脚本语法
check_script_syntax() {
    log_info "检查脚本语法..."
    
    local scripts=(
        "scripts/build-frontend.sh"
        "scripts/build-backend.sh"
        "scripts/build-all.sh"
        "scripts/generate-config.sh"
        "scripts/prepare-database.sh"
        "scripts/monitor.sh"
    )
    
    for script in "${scripts[@]}"; do
        log_check "检查语法: $script"
        
        if [ -f "$PROJECT_ROOT/$script" ]; then
            if bash -n "$PROJECT_ROOT/$script" 2>/dev/null; then
                log_success "$script 语法正确"
            else
                log_error "$script 语法错误"
            fi
        fi
    done
}

# 检查文档文件
check_documentation() {
    log_info "检查文档文件..."
    
    local docs=(
        "docs/troubleshooting.md"
        "README.md"
    )
    
    for doc in "${docs[@]}"; do
        log_check "检查文档: $doc"
        
        if [ -f "$PROJECT_ROOT/$doc" ]; then
            local word_count=$(wc -w < "$PROJECT_ROOT/$doc")
            if [ "$word_count" -gt 100 ]; then
                log_success "$doc 存在且内容充实 ($word_count 词)"
            else
                log_warning "$doc 存在但内容较少 ($word_count 词)"
            fi
        else
            log_error "$doc 不存在"
        fi
    done
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local required_dirs=(
        "jshERP-web"
        "jshERP-boot"
        "scripts"
        "docs"
    )
    
    for dir in "${required_dirs[@]}"; do
        log_check "检查目录: $dir"
        
        if [ -d "$PROJECT_ROOT/$dir" ]; then
            log_success "$dir 目录存在"
        else
            log_error "$dir 目录不存在"
        fi
    done
    
    # 检查关键文件
    local required_files=(
        "jshERP-web/package.json"
        "jshERP-boot/pom.xml"
        "jshERP-boot/src/main/resources/application.properties"
    )
    
    for file in "${required_files[@]}"; do
        log_check "检查文件: $file"
        
        if [ -f "$PROJECT_ROOT/$file" ]; then
            log_success "$file 存在"
        else
            log_error "$file 不存在"
        fi
    done
}

# 检查前端项目
check_frontend_project() {
    log_info "检查前端项目..."
    
    local frontend_dir="$PROJECT_ROOT/jshERP-web"
    
    if [ -d "$frontend_dir" ]; then
        cd "$frontend_dir"
        
        # 检查package.json
        log_check "检查前端package.json"
        if [ -f "package.json" ]; then
            if grep -q "vue" package.json && grep -q "ant-design-vue" package.json; then
                log_success "前端依赖配置正确"
            else
                log_warning "前端依赖配置可能有问题"
            fi
        else
            log_error "package.json不存在"
        fi
        
        # 检查构建脚本
        log_check "检查前端构建脚本"
        if grep -q "build" package.json; then
            log_success "前端构建脚本存在"
        else
            log_error "前端构建脚本不存在"
        fi
        
        # 检查Vue配置
        log_check "检查Vue配置文件"
        if [ -f "vue.config.js" ]; then
            log_success "Vue配置文件存在"
        else
            log_warning "Vue配置文件不存在"
        fi
        
        cd "$PROJECT_ROOT"
    fi
}

# 检查后端项目
check_backend_project() {
    log_info "检查后端项目..."
    
    local backend_dir="$PROJECT_ROOT/jshERP-boot"
    
    if [ -d "$backend_dir" ]; then
        cd "$backend_dir"
        
        # 检查pom.xml
        log_check "检查后端pom.xml"
        if [ -f "pom.xml" ]; then
            if grep -q "spring-boot" pom.xml && grep -q "mybatis" pom.xml; then
                log_success "后端依赖配置正确"
            else
                log_warning "后端依赖配置可能有问题"
            fi
        else
            log_error "pom.xml不存在"
        fi
        
        # 检查启动脚本
        log_check "检查后端启动脚本"
        local start_scripts=("start.sh" "restart.sh" "stop.sh")
        local script_count=0
        
        for script in "${start_scripts[@]}"; do
            if [ -f "$script" ]; then
                ((script_count++))
            fi
        done
        
        if [ "$script_count" -ge 2 ]; then
            log_success "后端启动脚本完整"
        else
            log_warning "后端启动脚本不完整"
        fi
        
        # 检查配置文件
        log_check "检查后端配置文件"
        if [ -f "src/main/resources/application.properties" ]; then
            log_success "后端配置文件存在"
        else
            log_error "后端配置文件不存在"
        fi
        
        cd "$PROJECT_ROOT"
    fi
}

# 检查数据库脚本
check_database_scripts() {
    log_info "检查数据库脚本..."
    
    # 检查SQL文件
    log_check "检查数据库初始化脚本"
    if [ -f "$PROJECT_ROOT/jshERP-boot/docs/jsh_erp.sql" ]; then
        local table_count=$(grep -c "CREATE TABLE" "$PROJECT_ROOT/jshERP-boot/docs/jsh_erp.sql" || echo "0")
        if [ "$table_count" -gt 20 ]; then
            log_success "数据库脚本完整 ($table_count 个表)"
        else
            log_warning "数据库脚本可能不完整 ($table_count 个表)"
        fi
    else
        log_error "数据库初始化脚本不存在"
    fi
}

# 检查权限配置
check_permissions() {
    log_info "检查权限配置..."
    
    # 检查权限修复脚本
    log_check "检查权限修复脚本"
    if [ -f "$PROJECT_ROOT/scripts/fix-button-permissions.sh" ]; then
        if grep -q "jsh_user_business" "$PROJECT_ROOT/scripts/fix-button-permissions.sh"; then
            log_success "权限修复脚本配置正确"
        else
            log_warning "权限修复脚本配置可能有问题"
        fi
    else
        log_error "权限修复脚本不存在"
    fi
}

# 检查构建环境
check_build_environment() {
    log_info "检查构建环境..."
    
    # 检查Node.js
    log_check "检查Node.js环境"
    if command -v node &> /dev/null; then
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -ge 14 ]; then
            log_success "Node.js版本符合要求 ($(node --version))"
        else
            log_warning "Node.js版本过低 ($(node --version))"
        fi
    else
        log_warning "Node.js未安装"
    fi
    
    # 检查Java
    log_check "检查Java环境"
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1-2)
        if [[ "$java_version" == "1.8" ]] || [[ "$java_version" > "1.8" ]]; then
            log_success "Java版本符合要求 ($java_version)"
        else
            log_warning "Java版本可能不符合要求 ($java_version)"
        fi
    else
        log_warning "Java未安装"
    fi
    
    # 检查Maven
    log_check "检查Maven环境"
    if command -v mvn &> /dev/null; then
        log_success "Maven已安装 ($(mvn --version | head -n 1))"
    else
        log_warning "Maven未安装"
    fi
}

# 性能基准测试
run_performance_test() {
    log_info "运行性能基准测试..."
    
    # 检查脚本执行时间
    log_check "测试脚本执行性能"
    
    local start_time=$(date +%s)
    "$PROJECT_ROOT/scripts/build-frontend.sh" --help > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$duration" -lt 5 ]; then
        log_success "脚本执行性能良好 (${duration}秒)"
    else
        log_warning "脚本执行较慢 (${duration}秒)"
    fi
}

# 生成质量报告
generate_quality_report() {
    local report_file="$PROJECT_ROOT/quality-report.txt"
    
    cat > "$report_file" << EOF
jshERP标准化部署方案质量检查报告
生成时间: $(date '+%Y-%m-%d %H:%M:%S')
========================================

检查统计:
- 总检查项: $TOTAL_CHECKS
- 通过: $PASSED_CHECKS
- 警告: $WARNING_CHECKS  
- 失败: $FAILED_CHECKS

质量评分: $(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))%

项目结构:
$(find "$PROJECT_ROOT" -type f -name "*.sh" -o -name "*.md" -o -name "*.json" -o -name "*.xml" | head -20)

构建环境:
- Node.js: $(command -v node &> /dev/null && node --version || echo "未安装")
- Java: $(command -v java &> /dev/null && java -version 2>&1 | head -n 1 || echo "未安装")
- Maven: $(command -v mvn &> /dev/null && mvn --version | head -n 1 || echo "未安装")

建议:
$(if [ $FAILED_CHECKS -gt 0 ]; then echo "- 修复 $FAILED_CHECKS 个失败项"; fi)
$(if [ $WARNING_CHECKS -gt 0 ]; then echo "- 关注 $WARNING_CHECKS 个警告项"; fi)
$(if [ $FAILED_CHECKS -eq 0 ] && [ $WARNING_CHECKS -eq 0 ]; then echo "- 质量检查全部通过，可以进行部署"; fi)

========================================
报告结束
EOF
    
    log_info "质量报告已生成: $report_file"
}

# 显示最终结果
show_final_result() {
    echo ""
    echo "=================================================="
    echo "    jshERP质量检查结果"
    echo "=================================================="
    echo "总检查项: $TOTAL_CHECKS"
    echo -e "通过: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "警告: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "失败: ${RED}$FAILED_CHECKS${NC}"
    echo ""
    
    local success_rate=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    echo "质量评分: $success_rate%"
    
    if [ $FAILED_CHECKS -eq 0 ] && [ $WARNING_CHECKS -eq 0 ]; then
        echo -e "${GREEN}✓ 质量检查全部通过，可以进行生产部署${NC}"
    elif [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${YELLOW}⚠ 存在警告项，建议优化后部署${NC}"
    else
        echo -e "${RED}✗ 存在失败项，需要修复后才能部署${NC}"
    fi
    
    echo "=================================================="
}

# 主函数
main() {
    echo "=================================================="
    echo "    jshERP标准化部署方案质量检查"
    echo "=================================================="
    echo ""
    
    # 执行所有检查
    check_scripts
    check_script_syntax
    check_documentation
    check_project_structure
    check_frontend_project
    check_backend_project
    check_database_scripts
    check_permissions
    check_build_environment
    run_performance_test
    
    # 生成报告和显示结果
    generate_quality_report
    show_final_result
}

# 执行主函数
main "$@"
