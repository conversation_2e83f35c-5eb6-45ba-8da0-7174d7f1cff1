#!/bin/bash

# jshERP性能监控脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 监控jshERP系统性能和健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
MONITOR_LOG="/opt/jshERP/logs/monitor.log"
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=80
ALERT_THRESHOLD_DISK=85
ALERT_THRESHOLD_RESPONSE=5000  # 毫秒

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$MONITOR_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [SUCCESS] $1" >> "$MONITOR_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARNING] $1" >> "$MONITOR_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$MONITOR_LOG"
}

log_metric() {
    echo -e "${CYAN}[METRIC]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [METRIC] $1" >> "$MONITOR_LOG"
}

# 创建监控日志目录
create_log_directory() {
    local log_dir=$(dirname "$MONITOR_LOG")
    if [ ! -d "$log_dir" ]; then
        mkdir -p "$log_dir"
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源使用情况..."
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}  # 去掉小数部分
    
    if [ "$cpu_usage" -gt "$ALERT_THRESHOLD_CPU" ]; then
        log_warning "CPU使用率过高: ${cpu_usage}%"
    else
        log_metric "CPU使用率: ${cpu_usage}%"
    fi
    
    # 内存使用率
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [ "$memory_usage" -gt "$ALERT_THRESHOLD_MEMORY" ]; then
        log_warning "内存使用率过高: ${memory_usage}%"
    else
        log_metric "内存使用率: ${memory_usage}%"
    fi
    
    # 磁盘使用率
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    if [ "$disk_usage" -gt "$ALERT_THRESHOLD_DISK" ]; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
    else
        log_metric "磁盘使用率: ${disk_usage}%"
    fi
    
    # 负载平均值
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    log_metric "系统负载: $load_avg"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Nginx
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未运行"
    fi
    
    # 检查MySQL
    if systemctl is-active --quiet mysql; then
        log_success "MySQL服务运行正常"
    else
        log_error "MySQL服务未运行"
    fi
    
    # 检查Redis
    if systemctl is-active --quiet redis; then
        log_success "Redis服务运行正常"
    else
        log_error "Redis服务未运行"
    fi
    
    # 检查jshERP应用
    if pgrep -f "jshERP.jar" > /dev/null; then
        log_success "jshERP应用运行正常"
        
        # 获取应用进程信息
        local app_pid=$(pgrep -f "jshERP.jar")
        local app_cpu=$(ps -p $app_pid -o %cpu --no-headers | tr -d ' ')
        local app_mem=$(ps -p $app_pid -o %mem --no-headers | tr -d ' ')
        
        log_metric "jshERP CPU使用率: ${app_cpu}%"
        log_metric "jshERP 内存使用率: ${app_mem}%"
    else
        log_error "jshERP应用未运行"
    fi
}

# 检查端口监听
check_ports() {
    log_info "检查端口监听状态..."
    
    # 检查关键端口
    local ports=("3000:Nginx" "9999:jshERP" "3306:MySQL" "6379:Redis")
    
    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d':' -f1)
        local service=$(echo $port_info | cut -d':' -f2)
        
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_success "$service 端口 $port 监听正常"
        else
            log_error "$service 端口 $port 未监听"
        fi
    done
}

# 检查应用响应时间
check_response_time() {
    log_info "检查应用响应时间..."
    
    # 检查前端响应时间
    local frontend_response=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000/ 2>/dev/null || echo "0")
    frontend_response=$(echo "$frontend_response * 1000" | bc 2>/dev/null || echo "0")
    frontend_response=${frontend_response%.*}
    
    if [ "$frontend_response" -gt "$ALERT_THRESHOLD_RESPONSE" ]; then
        log_warning "前端响应时间过长: ${frontend_response}ms"
    else
        log_metric "前端响应时间: ${frontend_response}ms"
    fi
    
    # 检查后端API响应时间
    local backend_response=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:9999/jshERP-boot/user/getCurrentUser 2>/dev/null || echo "0")
    backend_response=$(echo "$backend_response * 1000" | bc 2>/dev/null || echo "0")
    backend_response=${backend_response%.*}
    
    if [ "$backend_response" -gt "$ALERT_THRESHOLD_RESPONSE" ]; then
        log_warning "后端API响应时间过长: ${backend_response}ms"
    else
        log_metric "后端API响应时间: ${backend_response}ms"
    fi
}

# 检查数据库性能
check_database_performance() {
    log_info "检查数据库性能..."
    
    # 检查数据库连接数
    local connections=$(mysql -ujsh_user -p123456 -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | awk 'NR==2 {print $2}' || echo "0")
    log_metric "数据库连接数: $connections"
    
    # 检查慢查询数量
    local slow_queries=$(mysql -ujsh_user -p123456 -e "SHOW STATUS LIKE 'Slow_queries';" 2>/dev/null | awk 'NR==2 {print $2}' || echo "0")
    log_metric "慢查询数量: $slow_queries"
    
    # 检查数据库大小
    local db_size=$(mysql -ujsh_user -p123456 -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='jsh_erp';" 2>/dev/null | awk 'NR==2 {print $1}' || echo "0")
    log_metric "数据库大小: ${db_size}MB"
}

# 检查Redis性能
check_redis_performance() {
    log_info "检查Redis性能..."
    
    # 检查Redis内存使用
    local redis_memory=$(redis-cli -h 127.0.0.1 -p 6379 -a 1234abcd INFO memory 2>/dev/null | grep used_memory_human | cut -d':' -f2 | tr -d '\r' || echo "0")
    log_metric "Redis内存使用: $redis_memory"
    
    # 检查Redis连接数
    local redis_connections=$(redis-cli -h 127.0.0.1 -p 6379 -a 1234abcd INFO clients 2>/dev/null | grep connected_clients | cut -d':' -f2 | tr -d '\r' || echo "0")
    log_metric "Redis连接数: $redis_connections"
    
    # 检查Redis命令统计
    local redis_commands=$(redis-cli -h 127.0.0.1 -p 6379 -a 1234abcd INFO stats 2>/dev/null | grep total_commands_processed | cut -d':' -f2 | tr -d '\r' || echo "0")
    log_metric "Redis总命令数: $redis_commands"
}

# 检查日志文件大小
check_log_files() {
    log_info "检查日志文件大小..."
    
    local log_files=(
        "/opt/jshERP/logs/jshERP.log"
        "/opt/jshERP/logs/nginx_access.log"
        "/opt/jshERP/logs/nginx_error.log"
        "/var/log/mysql/error.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local file_size=$(du -sh "$log_file" 2>/dev/null | cut -f1)
            log_metric "日志文件 $(basename $log_file): $file_size"
            
            # 检查文件是否过大（超过100MB）
            local size_mb=$(du -m "$log_file" 2>/dev/null | cut -f1)
            if [ "$size_mb" -gt 100 ]; then
                log_warning "日志文件过大: $log_file ($file_size)"
            fi
        fi
    done
}

# 生成监控报告
generate_report() {
    local report_file="/opt/jshERP/logs/monitor_report_$(date '+%Y%m%d_%H%M%S').txt"
    
    cat > "$report_file" << EOF
jshERP系统监控报告
生成时间: $(date '+%Y-%m-%d %H:%M:%S')
========================================

系统信息:
- 主机名: $(hostname)
- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- 内核版本: $(uname -r)
- 运行时间: $(uptime | awk -F'up ' '{print $2}' | awk -F',' '{print $1}')

资源使用:
- CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
- 内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')
- 磁盘使用: $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5")"}')
- 系统负载: $(uptime | awk -F'load average:' '{print $2}')

服务状态:
- Nginx: $(systemctl is-active nginx)
- MySQL: $(systemctl is-active mysql)
- Redis: $(systemctl is-active redis)
- jshERP: $(pgrep -f "jshERP.jar" > /dev/null && echo "active" || echo "inactive")

网络状态:
- 前端端口3000: $(netstat -tlnp 2>/dev/null | grep -q ":3000 " && echo "监听中" || echo "未监听")
- 后端端口9999: $(netstat -tlnp 2>/dev/null | grep -q ":9999 " && echo "监听中" || echo "未监听")
- 数据库端口3306: $(netstat -tlnp 2>/dev/null | grep -q ":3306 " && echo "监听中" || echo "未监听")
- Redis端口6379: $(netstat -tlnp 2>/dev/null | grep -q ":6379 " && echo "监听中" || echo "未监听")

最近错误日志:
$(tail -10 /opt/jshERP/logs/jshERP.log 2>/dev/null | grep -i error || echo "无错误日志")

========================================
报告结束
EOF
    
    log_info "监控报告已生成: $report_file"
}

# 发送告警（可扩展）
send_alert() {
    local message="$1"
    local level="$2"
    
    # 这里可以扩展为发送邮件、短信、钉钉等告警
    log_error "告警: $message"
    
    # 示例：写入告警日志
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" >> "/opt/jshERP/logs/alerts.log"
}

# 主监控函数
run_monitoring() {
    echo "=================================================="
    echo "    jshERP系统监控"
    echo "    $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================="
    
    create_log_directory
    
    check_system_resources
    check_services
    check_ports
    check_response_time
    check_database_performance
    check_redis_performance
    check_log_files
    
    echo ""
    log_success "监控检查完成"
}

# 持续监控模式
continuous_monitoring() {
    local interval=${1:-300}  # 默认5分钟
    
    log_info "启动持续监控模式，检查间隔: ${interval}秒"
    
    while true; do
        run_monitoring
        sleep $interval
    done
}

# 显示帮助信息
show_help() {
    echo "jshERP性能监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --once              执行一次监控检查"
    echo "  --continuous [间隔]  持续监控模式（默认300秒）"
    echo "  --report            生成详细监控报告"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --once                    # 执行一次检查"
    echo "  $0 --continuous 600          # 每10分钟检查一次"
    echo "  $0 --report                  # 生成监控报告"
}

# 主函数
main() {
    case "${1:-}" in
        --once)
            run_monitoring
            ;;
        --continuous)
            continuous_monitoring "${2:-300}"
            ;;
        --report)
            run_monitoring
            generate_report
            ;;
        --help|-h)
            show_help
            ;;
        "")
            run_monitoring
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
