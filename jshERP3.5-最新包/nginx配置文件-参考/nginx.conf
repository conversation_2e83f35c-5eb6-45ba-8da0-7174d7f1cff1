
#user   nobody;
worker_processes  1;

#pid    logs/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    index x.html;
    autoindex on;
    server_names_hash_bucket_size 64;
    client_max_body_size 10m;

    sendfile        on;
    keepalive_timeout  65;

    access_log  logs/access.log;

    server {
        listen 3000;

        server_name  localhost;

        gzip on;
        gzip_min_length 100;
        gzip_types text/plain text/css application/xml application/javascript;
        gzip_vary on;
        
        location / {
            root   /home/<USER>/jshERP-web;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
		
        location /jshERP-boot/ {
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header REMOTE-HOST $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_pass http://localhost:9999/jshERP-boot/;
        }
    }
}